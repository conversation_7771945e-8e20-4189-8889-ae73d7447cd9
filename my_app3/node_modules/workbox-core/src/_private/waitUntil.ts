/*
  Copyright 2020 Google LLC
  Use of this source code is governed by an MIT-style
  license that can be found in the LICENSE file or at
  https://opensource.org/licenses/MIT.
*/

import '../_version.js';

/**
 * A utility method that makes it easier to use `event.waitUntil` with
 * async functions and return the result.
 *
 * @param {ExtendableEvent} event
 * @param {Function} asyncFn
 * @return {Function}
 * @private
 */
function waitUntil(
  event: ExtendableEvent,
  asyncFn: () => Promise<any>,
): Promise<any> {
  const returnPromise = asyncFn();
  event.waitUntil(returnPromise);
  return returnPromise;
}

export {waitUntil};
