{"name": "workbox-build", "version": "6.6.1", "description": "A module that integrates into your build process, helping you generate a manifest of local files that workbox-sw should precache.", "keywords": ["workbox", "workboxjs", "service worker", "caching", "fetch requests", "offline", "file manifest"], "engines": {"node": ">=16.0.0"}, "author": "Google's Web DevRel Team", "license": "MIT", "repository": "googlechrome/workbox", "bugs": "https://github.com/GoogleChrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "dependencies": {"@apideck/better-ajv-errors": "^0.3.1", "@babel/core": "^7.11.1", "@babel/preset-env": "^7.11.0", "@babel/runtime": "^7.11.2", "@rollup/plugin-babel": "^5.2.0", "@rollup/plugin-node-resolve": "^11.2.1", "@rollup/plugin-replace": "^2.4.1", "@surma/rollup-plugin-off-main-thread": "^2.2.3", "ajv": "^8.6.0", "common-tags": "^1.8.0", "fast-json-stable-stringify": "^2.1.0", "fs-extra": "^9.0.1", "glob": "^7.1.6", "lodash": "^4.17.20", "pretty-bytes": "^5.3.0", "rollup": "^2.43.1", "rollup-plugin-terser": "^7.0.0", "source-map": "^0.8.0-beta.0", "stringify-object": "^3.3.0", "strip-comments": "^2.0.1", "tempy": "^0.6.0", "upath": "^1.2.0", "workbox-background-sync": "6.6.1", "workbox-broadcast-update": "6.6.1", "workbox-cacheable-response": "6.6.1", "workbox-core": "6.6.1", "workbox-expiration": "6.6.1", "workbox-google-analytics": "6.6.1", "workbox-navigation-preload": "6.6.1", "workbox-precaching": "6.6.1", "workbox-range-requests": "6.6.1", "workbox-recipes": "6.6.1", "workbox-routing": "6.6.1", "workbox-strategies": "6.6.1", "workbox-streams": "6.6.1", "workbox-sw": "6.6.1", "workbox-window": "6.6.1"}, "main": "build/index.js", "workbox": {"packageType": "node_ts"}, "types": "build/index.d.ts", "devDependencies": {"@types/node": "^18.15.11"}, "gitHead": "4a3c20d121efd878f3982760620f640e5bd67250"}