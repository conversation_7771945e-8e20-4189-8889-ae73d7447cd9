# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.0](https://github.com/inspect-js/has-bigints/compare/v1.0.2...v1.1.0) - 2024-12-18

### Commits

- [meta] use `npmignore` to autogenerate an npmignore file [`a411cea`](https://github.com/inspect-js/has-bigints/commit/a411ceaf68bc297944c1627ec914455843195398)
- [actions] split out node 10-20, and 20+ [`4515878`](https://github.com/inspect-js/has-bigints/commit/45158780f412a6362d7a6116c552f75e23b4be38)
- [New] add types [`c888241`](https://github.com/inspect-js/has-bigints/commit/c888241a4fda933a270369419d11ce7f19ea50ce)
- [actions] update rebase action to use reusable workflow [`6f44338`](https://github.com/inspect-js/has-bigints/commit/6f44338ebca614230de3c87dc82719cf067335a2)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `npmignore`, `tape` [`ffa1e4d`](https://github.com/inspect-js/has-bigints/commit/ffa1e4daad689075fec091c86d6b53d4432b99ff)
- [Dev Deps] update `aud`, `tape` [`0f5d096`](https://github.com/inspect-js/has-bigints/commit/0f5d09697b37c182587313e4a9218353f8d3d22b)
- [meta] add missing `engines.node` [`3f73c71`](https://github.com/inspect-js/has-bigints/commit/3f73c71d7f4f13dfcec5520a51a97fb91645d9db)
- [Tests] replace `aud` with `npm audit` [`b007efd`](https://github.com/inspect-js/has-bigints/commit/b007efdd11608912a5dcae216748840a746d3517)
- [Dev Deps] add missing peer dep [`459c612`](https://github.com/inspect-js/has-bigints/commit/459c6126ecff63021664dbb48d19c6b1ddc2db14)

## [v1.0.2](https://github.com/inspect-js/has-bigints/compare/v1.0.1...v1.0.2) - 2022-04-19

### Commits

- [actions] reuse common workflows [`a655b7f`](https://github.com/inspect-js/has-bigints/commit/a655b7f7733ba2de078b3a59a704c2795440d08c)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`730a2e5`](https://github.com/inspect-js/has-bigints/commit/730a2e53d3f5f82ac43a11cb9753b11445d06d58)
- [readme] add github actions/codecov badges; update URLs [`9a83788`](https://github.com/inspect-js/has-bigints/commit/9a8378861917b41ebb360a9d8ab3d39aa33acf7b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`, `tape` [`b1edc52`](https://github.com/inspect-js/has-bigints/commit/b1edc522ef6c23556e55b9c81b65fe884d1e2cc4)
- [actions] update codecov uploader [`cbb1bd0`](https://github.com/inspect-js/has-bigints/commit/cbb1bd0eff486070a19283238da6afd33ca73b4b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`8717e6d`](https://github.com/inspect-js/has-bigints/commit/8717e6d6ae6b5010aea1fac5e48dfdfaf35d8c72)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog`, `safe-publish-latest`, `tape` [`5f70eab`](https://github.com/inspect-js/has-bigints/commit/5f70eab763118d40415f13e47446ea5c011fbe18)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`a1446bc`](https://github.com/inspect-js/has-bigints/commit/a1446bc3e806ce3911c73d6fbdd981f461c41019)
- [meta] use `prepublishOnly` script for npm 7+ [`f2dd197`](https://github.com/inspect-js/has-bigints/commit/f2dd19716a06ca7a971644761e864f0435db278e)
- [actions] use checkout v3 [`1ba72f1`](https://github.com/inspect-js/has-bigints/commit/1ba72f19ada076791ff193aca9d8537388d67ddb)
- [Refactor] use a global variable to get the original BigInt instead of a global property [`a7ccfac`](https://github.com/inspect-js/has-bigints/commit/a7ccfac0414e56dadd263fd07c0b5141388df502)
- [actions] skip `npm ls` on older nodes [`62d31e7`](https://github.com/inspect-js/has-bigints/commit/62d31e79658b16391458d0728e0dacbee8694ebb)

## [v1.0.1](https://github.com/inspect-js/has-bigints/compare/v1.0.0...v1.0.1) - 2020-12-13

### Commits

- [Tests] use shared travis-ci configs [`46a0d6b`](https://github.com/inspect-js/has-bigints/commit/46a0d6be7ed83bd7f5ead11e4eab7fc91570a448)
- [Tests] migrate tests to Github Actions [`91a38fa`](https://github.com/inspect-js/has-bigints/commit/91a38fa4b85a420b8cf4926b3799e6ceb60d8690)
- [meta] do not publish github action workflow files [`69aacba`](https://github.com/inspect-js/has-bigints/commit/69aacba320c1221e7fee1c71bde600bce063f24b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`64e2c08`](https://github.com/inspect-js/has-bigints/commit/64e2c0895b21ac91a137452fd2455932f62a2fc1)
- [Tests] run `nyc` on all tests; use `tape` runner [`8009375`](https://github.com/inspect-js/has-bigints/commit/8009375e5ec9faca6bbc09441002af5c5e59ff20)
- [actions] add automatic rebasing / merge commit blocking [`e599917`](https://github.com/inspect-js/has-bigints/commit/e599917fd1f751c9a6c0daac70acb243f8c3a01d)
- [actions] add "Allow Edits" workflow [`bd0126e`](https://github.com/inspect-js/has-bigints/commit/bd0126eba2d67e9b9d588bced34413f507698154)
- [readme] remove travis badge [`8e02a73`](https://github.com/inspect-js/has-bigints/commit/8e02a73db34827d24d2945f2db822973a0b49925)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog`, `safe-publish-latest` [`95859f2`](https://github.com/inspect-js/has-bigints/commit/95859f28f23f5733481c52a501063802cf64f75b)
- [Dev Deps] update `auto-changelog`, `in-publish`, `tape` [`0588f41`](https://github.com/inspect-js/has-bigints/commit/0588f415c6cc01d6b34668680044e03b2998e03f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`5b024a6`](https://github.com/inspect-js/has-bigints/commit/5b024a664a8b7d2d2f750a4c11ce20c395b6f12a)
- [meta] add `version` scripts [`4788d61`](https://github.com/inspect-js/has-bigints/commit/4788d61101c009e4e2c1b4d944c263de06192c6a)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`be0e0de`](https://github.com/inspect-js/has-bigints/commit/be0e0de08298dfe483c5d7a2675e5133abeabc53)
- [Dev Deps] update `auto-changelog`; add `aud` [`13a8d1b`](https://github.com/inspect-js/has-bigints/commit/13a8d1bf1f661871d890bfa174de9514f016cdd9)
- [actions] fix action name [`f873d9a`](https://github.com/inspect-js/has-bigints/commit/f873d9a2f10718662528a755b12c61202f4e4afa)
- [meta] add `funding` field [`1b51f49`](https://github.com/inspect-js/has-bigints/commit/1b51f4921df1faf85d2679a0e4ba97ef015a73b7)
- [Dev Deps] update `auto-changelog` [`2322461`](https://github.com/inspect-js/has-bigints/commit/2322461789810434c447439f155eb3ca23eb29fb)
- [Tests] only audit prod deps [`aabdade`](https://github.com/inspect-js/has-bigints/commit/aabdadeaa1e126b91a2fbd82263cc49651ff5e7b)

## v1.0.0 - 2019-08-10

### Commits

- [Tests] add `.travis.yml` [`9730412`](https://github.com/inspect-js/has-bigints/commit/973041241dc172474bb9457aad41790fe54fec88)
- Initial commit [`65f7c38`](https://github.com/inspect-js/has-bigints/commit/65f7c3889d9a98e214e26d650723cbfc49338463)
- [Tests] add tests [`e374a78`](https://github.com/inspect-js/has-bigints/commit/e374a78033d457badcd47e06752fdec7f62e6c39)
- readme [`5d39092`](https://github.com/inspect-js/has-bigints/commit/5d3909249da442867180fb747eef27543627d250)
- npm init [`1be2e3d`](https://github.com/inspect-js/has-bigints/commit/1be2e3d69db6718901e6845cfc38a07cc46dfd96)
- implementation [`b7bc812`](https://github.com/inspect-js/has-bigints/commit/b7bc8121db1fb1a827625c4cb0608935e3dcbe31)
- [Tests] add linting [`04533be`](https://github.com/inspect-js/has-bigints/commit/04533bef57f60e322238f71f32ee3ae0c988bac4)
- [meta] create FUNDING.yml [`cf824a7`](https://github.com/inspect-js/has-bigints/commit/cf824a7d02e867957d8db17ee0a4c70c8ee5ff23)
- Only apps should have lockfiles [`64e8242`](https://github.com/inspect-js/has-bigints/commit/64e82429f1dca99f624dc971ff13516dee28d353)
