{"name": "workbox-google-analytics", "version": "6.6.1", "license": "MIT", "author": "Google's Web DevRel Team", "description": "Queues failed requests and uses the Background Sync API to replay them when the network is available", "repository": "googlechrome/workbox", "bugs": "https://github.com/googlechrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "keywords": ["workbox", "workboxjs", "service worker", "sw", "offline", "google", "analytics"], "workbox": {"browserNamespace": "workbox.googleAnalytics", "outputFilename": "workbox-offline-ga", "packageType": "sw"}, "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "dependencies": {"workbox-background-sync": "6.6.1", "workbox-core": "6.6.1", "workbox-routing": "6.6.1", "workbox-strategies": "6.6.1"}, "gitHead": "4a3c20d121efd878f3982760620f640e5bd67250"}