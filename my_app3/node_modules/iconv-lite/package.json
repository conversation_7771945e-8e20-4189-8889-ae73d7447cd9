{"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.24", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": "<PERSON> <<EMAIL>>", "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": "https://github.com/ashtuchkin/iconv-lite/issues", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.87.0", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}}