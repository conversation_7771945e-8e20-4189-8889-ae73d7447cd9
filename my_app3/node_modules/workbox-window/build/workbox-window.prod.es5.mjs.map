{"version": 3, "file": "workbox-window.prod.es5.mjs", "sources": ["../_version.js", "../messageSW.js", "../../workbox-core/_version.js", "../../workbox-core/_private/Deferred.js", "../utils/urlsMatch.js", "../utils/WorkboxEvent.js", "../Workbox.js", "../../workbox-core/_private/dontWaitFor.js", "../utils/WorkboxEventTarget.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:window:6.6.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport './_version.js';\n/**\n * Sends a data object to a service worker via `postMessage` and resolves with\n * a response (if any).\n *\n * A response can be set in a message handler in the service worker by\n * calling `event.ports[0].postMessage(...)`, which will resolve the promise\n * returned by `messageSW()`. If no response is set, the promise will not\n * resolve.\n *\n * @param {ServiceWorker} sw The service worker to send the message to.\n * @param {Object} data An object to send to the service worker.\n * @return {Promise<Object|undefined>}\n * @memberof workbox-window\n */\n// Better not change type of data.\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction messageSW(sw, data) {\n    return new Promise((resolve) => {\n        const messageChannel = new MessageChannel();\n        messageChannel.port1.onmessage = (event) => {\n            resolve(event.data);\n        };\n        sw.postMessage(data, [messageChannel.port2]);\n    });\n}\nexport { messageSW };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:core:6.6.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The Deferred class composes Promises in a way that allows for them to be\n * resolved or rejected from outside the constructor. In most cases promises\n * should be used directly, but Deferreds can be necessary when the logic to\n * resolve a promise must be separate.\n *\n * @private\n */\nclass Deferred {\n    /**\n     * Creates a promise and exposes its resolve and reject functions as methods.\n     */\n    constructor() {\n        this.promise = new Promise((resolve, reject) => {\n            this.resolve = resolve;\n            this.reject = reject;\n        });\n    }\n}\nexport { Deferred };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Returns true if two URLs have the same `.href` property. The URLS can be\n * relative, and if they are the current location href is used to resolve URLs.\n *\n * @private\n * @param {string} url1\n * @param {string} url2\n * @return {boolean}\n */\nexport function urlsMatch(url1, url2) {\n    const { href } = location;\n    return new URL(url1, href).href === new URL(url2, href).href;\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A minimal `Event` subclass shim.\n * This doesn't *actually* subclass `Event` because not all browsers support\n * constructable `EventTarget`, and using a real `Event` will error.\n * @private\n */\nexport class WorkboxEvent {\n    constructor(type, props) {\n        this.type = type;\n        Object.assign(this, props);\n    }\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { Deferred } from 'workbox-core/_private/Deferred.js';\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { messageSW } from './messageSW.js';\nimport { WorkboxEventTarget } from './utils/WorkboxEventTarget.js';\nimport { urlsMatch } from './utils/urlsMatch.js';\nimport { WorkboxEvent } from './utils/WorkboxEvent.js';\nimport './_version.js';\n// The time a SW must be in the waiting phase before we can conclude\n// `skipWaiting()` wasn't called. This 200 amount wasn't scientifically\n// chosen, but it seems to avoid false positives in my testing.\nconst WAITING_TIMEOUT_DURATION = 200;\n// The amount of time after a registration that we can reasonably conclude\n// that the registration didn't trigger an update.\nconst REGISTRATION_TIMEOUT_DURATION = 60000;\n// The de facto standard message that a service worker should be listening for\n// to trigger a call to skipWaiting().\nconst SKIP_WAITING_MESSAGE = { type: 'SKIP_WAITING' };\n/**\n * A class to aid in handling service worker registration, updates, and\n * reacting to service worker lifecycle events.\n *\n * @fires {@link workbox-window.Workbox#message}\n * @fires {@link workbox-window.Workbox#installed}\n * @fires {@link workbox-window.Workbox#waiting}\n * @fires {@link workbox-window.Workbox#controlling}\n * @fires {@link workbox-window.Workbox#activated}\n * @fires {@link workbox-window.Workbox#redundant}\n * @memberof workbox-window\n */\nclass Workbox extends WorkboxEventTarget {\n    /**\n     * Creates a new Workbox instance with a script URL and service worker\n     * options. The script URL and options are the same as those used when\n     * calling [navigator.serviceWorker.register(scriptURL, options)](https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/register).\n     *\n     * @param {string|TrustedScriptURL} scriptURL The service worker script\n     *     associated with this instance. Using a\n     *     [`TrustedScriptURL`](https://web.dev/trusted-types/) is supported.\n     * @param {Object} [registerOptions] The service worker options associated\n     *     with this instance.\n     */\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    constructor(scriptURL, registerOptions = {}) {\n        super();\n        this._registerOptions = {};\n        this._updateFoundCount = 0;\n        // Deferreds we can resolve later.\n        this._swDeferred = new Deferred();\n        this._activeDeferred = new Deferred();\n        this._controllingDeferred = new Deferred();\n        this._registrationTime = 0;\n        this._ownSWs = new Set();\n        /**\n         * @private\n         */\n        this._onUpdateFound = () => {\n            // `this._registration` will never be `undefined` after an update is found.\n            const registration = this._registration;\n            const installingSW = registration.installing;\n            // If the script URL passed to `navigator.serviceWorker.register()` is\n            // different from the current controlling SW's script URL, we know any\n            // successful registration calls will trigger an `updatefound` event.\n            // But if the registered script URL is the same as the current controlling\n            // SW's script URL, we'll only get an `updatefound` event if the file\n            // changed since it was last registered. This can be a problem if the user\n            // opens up the same page in a different tab, and that page registers\n            // a SW that triggers an update. It's a problem because this page has no\n            // good way of knowing whether the `updatefound` event came from the SW\n            // script it registered or from a registration attempt made by a newer\n            // version of the page running in another tab.\n            // To minimize the possibility of a false positive, we use the logic here:\n            const updateLikelyTriggeredExternally = \n            // Since we enforce only calling `register()` once, and since we don't\n            // add the `updatefound` event listener until the `register()` call, if\n            // `_updateFoundCount` is > 0 then it means this method has already\n            // been called, thus this SW must be external\n            this._updateFoundCount > 0 ||\n                // If the script URL of the installing SW is different from this\n                // instance's script URL, we know it's definitely not from our\n                // registration.\n                !urlsMatch(installingSW.scriptURL, this._scriptURL.toString()) ||\n                // If all of the above are false, then we use a time-based heuristic:\n                // Any `updatefound` event that occurs long after our registration is\n                // assumed to be external.\n                performance.now() > this._registrationTime + REGISTRATION_TIMEOUT_DURATION\n                ? // If any of the above are not true, we assume the update was\n                    // triggered by this instance.\n                    true\n                : false;\n            if (updateLikelyTriggeredExternally) {\n                this._externalSW = installingSW;\n                registration.removeEventListener('updatefound', this._onUpdateFound);\n            }\n            else {\n                // If the update was not triggered externally we know the installing\n                // SW is the one we registered, so we set it.\n                this._sw = installingSW;\n                this._ownSWs.add(installingSW);\n                this._swDeferred.resolve(installingSW);\n                // The `installing` state isn't something we have a dedicated\n                // callback for, but we do log messages for it in development.\n                if (process.env.NODE_ENV !== 'production') {\n                    if (navigator.serviceWorker.controller) {\n                        logger.log('Updated service worker found. Installing now...');\n                    }\n                    else {\n                        logger.log('Service worker is installing...');\n                    }\n                }\n            }\n            // Increment the `updatefound` count, so future invocations of this\n            // method can be sure they were triggered externally.\n            ++this._updateFoundCount;\n            // Add a `statechange` listener regardless of whether this update was\n            // triggered externally, since we have callbacks for both.\n            installingSW.addEventListener('statechange', this._onStateChange);\n        };\n        /**\n         * @private\n         * @param {Event} originalEvent\n         */\n        this._onStateChange = (originalEvent) => {\n            // `this._registration` will never be `undefined` after an update is found.\n            const registration = this._registration;\n            const sw = originalEvent.target;\n            const { state } = sw;\n            const isExternal = sw === this._externalSW;\n            const eventProps = {\n                sw,\n                isExternal,\n                originalEvent,\n            };\n            if (!isExternal && this._isUpdate) {\n                eventProps.isUpdate = true;\n            }\n            this.dispatchEvent(new WorkboxEvent(state, eventProps));\n            if (state === 'installed') {\n                // This timeout is used to ignore cases where the service worker calls\n                // `skipWaiting()` in the install event, thus moving it directly in the\n                // activating state. (Since all service workers *must* go through the\n                // waiting phase, the only way to detect `skipWaiting()` called in the\n                // install event is to observe that the time spent in the waiting phase\n                // is very short.)\n                // NOTE: we don't need separate timeouts for the own and external SWs\n                // since they can't go through these phases at the same time.\n                this._waitingTimeout = self.setTimeout(() => {\n                    // Ensure the SW is still waiting (it may now be redundant).\n                    if (state === 'installed' && registration.waiting === sw) {\n                        this.dispatchEvent(new WorkboxEvent('waiting', eventProps));\n                        if (process.env.NODE_ENV !== 'production') {\n                            if (isExternal) {\n                                logger.warn('An external service worker has installed but is ' +\n                                    'waiting for this client to close before activating...');\n                            }\n                            else {\n                                logger.warn('The service worker has installed but is waiting ' +\n                                    'for existing clients to close before activating...');\n                            }\n                        }\n                    }\n                }, WAITING_TIMEOUT_DURATION);\n            }\n            else if (state === 'activating') {\n                clearTimeout(this._waitingTimeout);\n                if (!isExternal) {\n                    this._activeDeferred.resolve(sw);\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                switch (state) {\n                    case 'installed':\n                        if (isExternal) {\n                            logger.warn('An external service worker has installed. ' +\n                                'You may want to suggest users reload this page.');\n                        }\n                        else {\n                            logger.log('Registered service worker installed.');\n                        }\n                        break;\n                    case 'activated':\n                        if (isExternal) {\n                            logger.warn('An external service worker has activated.');\n                        }\n                        else {\n                            logger.log('Registered service worker activated.');\n                            if (sw !== navigator.serviceWorker.controller) {\n                                logger.warn('The registered service worker is active but ' +\n                                    'not yet controlling the page. Reload or run ' +\n                                    '`clients.claim()` in the service worker.');\n                            }\n                        }\n                        break;\n                    case 'redundant':\n                        if (sw === this._compatibleControllingSW) {\n                            logger.log('Previously controlling service worker now redundant!');\n                        }\n                        else if (!isExternal) {\n                            logger.log('Registered service worker now redundant!');\n                        }\n                        break;\n                }\n            }\n        };\n        /**\n         * @private\n         * @param {Event} originalEvent\n         */\n        this._onControllerChange = (originalEvent) => {\n            const sw = this._sw;\n            const isExternal = sw !== navigator.serviceWorker.controller;\n            // Unconditionally dispatch the controlling event, with isExternal set\n            // to distinguish between controller changes due to the initial registration\n            // vs. an update-check or other tab's registration.\n            // See https://github.com/GoogleChrome/workbox/issues/2786\n            this.dispatchEvent(new WorkboxEvent('controlling', {\n                isExternal,\n                originalEvent,\n                sw,\n                isUpdate: this._isUpdate,\n            }));\n            if (!isExternal) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log('Registered service worker now controlling this page.');\n                }\n                this._controllingDeferred.resolve(sw);\n            }\n        };\n        /**\n         * @private\n         * @param {Event} originalEvent\n         */\n        this._onMessage = async (originalEvent) => {\n            // Can't change type 'any' of data.\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            const { data, ports, source } = originalEvent;\n            // Wait until there's an \"own\" service worker. This is used to buffer\n            // `message` events that may be received prior to calling `register()`.\n            await this.getSW();\n            // If the service worker that sent the message is in the list of own\n            // service workers for this instance, dispatch a `message` event.\n            // NOTE: we check for all previously owned service workers rather than\n            // just the current one because some messages (e.g. cache updates) use\n            // a timeout when sent and may be delayed long enough for a service worker\n            // update to be found.\n            if (this._ownSWs.has(source)) {\n                this.dispatchEvent(new WorkboxEvent('message', {\n                    // Can't change type 'any' of data.\n                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                    data,\n                    originalEvent,\n                    ports,\n                    sw: source,\n                }));\n            }\n        };\n        this._scriptURL = scriptURL;\n        this._registerOptions = registerOptions;\n        // Add a message listener immediately since messages received during\n        // page load are buffered only until the DOMContentLoaded event:\n        // https://github.com/GoogleChrome/workbox/issues/2202\n        navigator.serviceWorker.addEventListener('message', this._onMessage);\n    }\n    /**\n     * Registers a service worker for this instances script URL and service\n     * worker options. By default this method delays registration until after\n     * the window has loaded.\n     *\n     * @param {Object} [options]\n     * @param {Function} [options.immediate=false] Setting this to true will\n     *     register the service worker immediately, even if the window has\n     *     not loaded (not recommended).\n     */\n    async register({ immediate = false } = {}) {\n        if (process.env.NODE_ENV !== 'production') {\n            if (this._registrationTime) {\n                logger.error('Cannot re-register a Workbox instance after it has ' +\n                    'been registered. Create a new instance instead.');\n                return;\n            }\n        }\n        if (!immediate && document.readyState !== 'complete') {\n            await new Promise((res) => window.addEventListener('load', res));\n        }\n        // Set this flag to true if any service worker was controlling the page\n        // at registration time.\n        this._isUpdate = Boolean(navigator.serviceWorker.controller);\n        // Before registering, attempt to determine if a SW is already controlling\n        // the page, and if that SW script (and version, if specified) matches this\n        // instance's script.\n        this._compatibleControllingSW = this._getControllingSWIfCompatible();\n        this._registration = await this._registerScript();\n        // If we have a compatible controller, store the controller as the \"own\"\n        // SW, resolve active/controlling deferreds and add necessary listeners.\n        if (this._compatibleControllingSW) {\n            this._sw = this._compatibleControllingSW;\n            this._activeDeferred.resolve(this._compatibleControllingSW);\n            this._controllingDeferred.resolve(this._compatibleControllingSW);\n            this._compatibleControllingSW.addEventListener('statechange', this._onStateChange, { once: true });\n        }\n        // If there's a waiting service worker with a matching URL before the\n        // `updatefound` event fires, it likely means that this site is open\n        // in another tab, or the user refreshed the page (and thus the previous\n        // page wasn't fully unloaded before this page started loading).\n        // https://developers.google.com/web/fundamentals/primers/service-workers/lifecycle#waiting\n        const waitingSW = this._registration.waiting;\n        if (waitingSW &&\n            urlsMatch(waitingSW.scriptURL, this._scriptURL.toString())) {\n            // Store the waiting SW as the \"own\" Sw, even if it means overwriting\n            // a compatible controller.\n            this._sw = waitingSW;\n            // Run this in the next microtask, so any code that adds an event\n            // listener after awaiting `register()` will get this event.\n            dontWaitFor(Promise.resolve().then(() => {\n                this.dispatchEvent(new WorkboxEvent('waiting', {\n                    sw: waitingSW,\n                    wasWaitingBeforeRegister: true,\n                }));\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.warn('A service worker was already waiting to activate ' +\n                        'before this script was registered...');\n                }\n            }));\n        }\n        // If an \"own\" SW is already set, resolve the deferred.\n        if (this._sw) {\n            this._swDeferred.resolve(this._sw);\n            this._ownSWs.add(this._sw);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log('Successfully registered service worker.', this._scriptURL.toString());\n            if (navigator.serviceWorker.controller) {\n                if (this._compatibleControllingSW) {\n                    logger.debug('A service worker with the same script URL ' +\n                        'is already controlling this page.');\n                }\n                else {\n                    logger.debug('A service worker with a different script URL is ' +\n                        'currently controlling the page. The browser is now fetching ' +\n                        'the new script now...');\n                }\n            }\n            const currentPageIsOutOfScope = () => {\n                const scopeURL = new URL(this._registerOptions.scope || this._scriptURL.toString(), document.baseURI);\n                const scopeURLBasePath = new URL('./', scopeURL.href).pathname;\n                return !location.pathname.startsWith(scopeURLBasePath);\n            };\n            if (currentPageIsOutOfScope()) {\n                logger.warn('The current page is not in scope for the registered ' +\n                    'service worker. Was this a mistake?');\n            }\n        }\n        this._registration.addEventListener('updatefound', this._onUpdateFound);\n        navigator.serviceWorker.addEventListener('controllerchange', this._onControllerChange);\n        return this._registration;\n    }\n    /**\n     * Checks for updates of the registered service worker.\n     */\n    async update() {\n        if (!this._registration) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.error('Cannot update a Workbox instance without ' +\n                    'being registered. Register the Workbox instance first.');\n            }\n            return;\n        }\n        // Try to update registration\n        await this._registration.update();\n    }\n    /**\n     * Resolves to the service worker registered by this instance as soon as it\n     * is active. If a service worker was already controlling at registration\n     * time then it will resolve to that if the script URLs (and optionally\n     * script versions) match, otherwise it will wait until an update is found\n     * and activates.\n     *\n     * @return {Promise<ServiceWorker>}\n     */\n    get active() {\n        return this._activeDeferred.promise;\n    }\n    /**\n     * Resolves to the service worker registered by this instance as soon as it\n     * is controlling the page. If a service worker was already controlling at\n     * registration time then it will resolve to that if the script URLs (and\n     * optionally script versions) match, otherwise it will wait until an update\n     * is found and starts controlling the page.\n     * Note: the first time a service worker is installed it will active but\n     * not start controlling the page unless `clients.claim()` is called in the\n     * service worker.\n     *\n     * @return {Promise<ServiceWorker>}\n     */\n    get controlling() {\n        return this._controllingDeferred.promise;\n    }\n    /**\n     * Resolves with a reference to a service worker that matches the script URL\n     * of this instance, as soon as it's available.\n     *\n     * If, at registration time, there's already an active or waiting service\n     * worker with a matching script URL, it will be used (with the waiting\n     * service worker taking precedence over the active service worker if both\n     * match, since the waiting service worker would have been registered more\n     * recently).\n     * If there's no matching active or waiting service worker at registration\n     * time then the promise will not resolve until an update is found and starts\n     * installing, at which point the installing service worker is used.\n     *\n     * @return {Promise<ServiceWorker>}\n     */\n    getSW() {\n        // If `this._sw` is set, resolve with that as we want `getSW()` to\n        // return the correct (new) service worker if an update is found.\n        return this._sw !== undefined\n            ? Promise.resolve(this._sw)\n            : this._swDeferred.promise;\n    }\n    /**\n     * Sends the passed data object to the service worker registered by this\n     * instance (via {@link workbox-window.Workbox#getSW}) and resolves\n     * with a response (if any).\n     *\n     * A response can be set in a message handler in the service worker by\n     * calling `event.ports[0].postMessage(...)`, which will resolve the promise\n     * returned by `messageSW()`. If no response is set, the promise will never\n     * resolve.\n     *\n     * @param {Object} data An object to send to the service worker\n     * @return {Promise<Object>}\n     */\n    // We might be able to change the 'data' type to Record<string, unknown> in the future.\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    async messageSW(data) {\n        const sw = await this.getSW();\n        return messageSW(sw, data);\n    }\n    /**\n     * Sends a `{type: 'SKIP_WAITING'}` message to the service worker that's\n     * currently in the `waiting` state associated with the current registration.\n     *\n     * If there is no current registration or no service worker is `waiting`,\n     * calling this will have no effect.\n     */\n    messageSkipWaiting() {\n        if (this._registration && this._registration.waiting) {\n            void messageSW(this._registration.waiting, SKIP_WAITING_MESSAGE);\n        }\n    }\n    /**\n     * Checks for a service worker already controlling the page and returns\n     * it if its script URL matches.\n     *\n     * @private\n     * @return {ServiceWorker|undefined}\n     */\n    _getControllingSWIfCompatible() {\n        const controller = navigator.serviceWorker.controller;\n        if (controller &&\n            urlsMatch(controller.scriptURL, this._scriptURL.toString())) {\n            return controller;\n        }\n        else {\n            return undefined;\n        }\n    }\n    /**\n     * Registers a service worker for this instances script URL and register\n     * options and tracks the time registration was complete.\n     *\n     * @private\n     */\n    async _registerScript() {\n        try {\n            // this._scriptURL may be a TrustedScriptURL, but there's no support for\n            // passing that to register() in lib.dom right now.\n            // https://github.com/GoogleChrome/workbox/issues/2855\n            const reg = await navigator.serviceWorker.register(this._scriptURL, this._registerOptions);\n            // Keep track of when registration happened, so it can be used in the\n            // `this._onUpdateFound` heuristic. Also use the presence of this\n            // property as a way to see if `.register()` has been called.\n            this._registrationTime = performance.now();\n            return reg;\n        }\n        catch (error) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.error(error);\n            }\n            // Re-throw the error.\n            throw error;\n        }\n    }\n}\nexport { Workbox };\n// The jsdoc comments below outline the events this instance may dispatch:\n// -----------------------------------------------------------------------\n/**\n * The `message` event is dispatched any time a `postMessage` is received.\n *\n * @event workbox-window.Workbox#message\n * @type {WorkboxEvent}\n * @property {*} data The `data` property from the original `message` event.\n * @property {Event} originalEvent The original [`message`]{@link https://developer.mozilla.org/en-US/docs/Web/API/MessageEvent}\n *     event.\n * @property {string} type `message`.\n * @property {MessagePort[]} ports The `ports` value from `originalEvent`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `installed` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * {@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw|registered service worker}\n * changes to `installed`.\n *\n * Then can happen either the very first time a service worker is installed,\n * or after an update to the current service worker is found. In the case\n * of an update being found, the event's `isUpdate` property will be `true`.\n *\n * @event workbox-window.Workbox#installed\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {string} type `installed`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `waiting` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * [registered service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw}\n * changes to `installed` and then doesn't immediately change to `activating`.\n * It may also be dispatched if a service worker with the same\n * [`scriptURL`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/scriptURL}\n * was already waiting when the {@link workbox-window.Workbox#register}\n * method was called.\n *\n * @event workbox-window.Workbox#waiting\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event|undefined} originalEvent The original\n *    [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event, or `undefined` in the case where the service worker was waiting\n *     to before `.register()` was called.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {boolean|undefined} wasWaitingBeforeRegister True if a service worker with\n *     a matching `scriptURL` was already waiting when this `Workbox`\n *     instance called `register()`.\n * @property {string} type `waiting`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `controlling` event is dispatched if a\n * [`controllerchange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/oncontrollerchange}\n * fires on the service worker [container]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer}\n * and the [`scriptURL`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/scriptURL}\n * of the new [controller]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/controller}\n * matches the `scriptURL` of the `Workbox` instance's\n * [registered service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw}.\n *\n * @event workbox-window.Workbox#controlling\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`controllerchange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/oncontrollerchange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this service worker was registered.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {string} type `controlling`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `activated` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * {@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw|registered service worker}\n * changes to `activated`.\n *\n * @event workbox-window.Workbox#activated\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {string} type `activated`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `redundant` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * [registered service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw}\n * changes to `redundant`.\n *\n * @event workbox-window.Workbox#redundant\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {string} type `redundant`.\n * @property {Workbox} target The `Workbox` instance.\n */\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A helper function that prevents a promise from being flagged as unused.\n *\n * @private\n **/\nexport function dontWaitFor(promise) {\n    // Effective no-op.\n    void promise.then(() => { });\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\n/**\n * A minimal `EventTarget` shim.\n * This is necessary because not all browsers support constructable\n * `EventTarget`, so using a real `EventTarget` will error.\n * @private\n */\nexport class WorkboxEventTarget {\n    constructor() {\n        this._eventListenerRegistry = new Map();\n    }\n    /**\n     * @param {string} type\n     * @param {Function} listener\n     * @private\n     */\n    addEventListener(type, listener) {\n        const foo = this._getEventListenersByType(type);\n        foo.add(listener);\n    }\n    /**\n     * @param {string} type\n     * @param {Function} listener\n     * @private\n     */\n    removeEventListener(type, listener) {\n        this._getEventListenersByType(type).delete(listener);\n    }\n    /**\n     * @param {Object} event\n     * @private\n     */\n    dispatchEvent(event) {\n        event.target = this;\n        const listeners = this._getEventListenersByType(event.type);\n        for (const listener of listeners) {\n            listener(event);\n        }\n    }\n    /**\n     * Returns a Set of listeners associated with the passed event type.\n     * If no handlers have been registered, an empty Set is returned.\n     *\n     * @param {string} type The event type.\n     * @return {Set<ListenerCallback>} An array of handler functions.\n     * @private\n     */\n    _getEventListenersByType(type) {\n        if (!this._eventListenerRegistry.has(type)) {\n            this._eventListenerRegistry.set(type, new Set());\n        }\n        return this._eventListenerRegistry.get(type);\n    }\n}\n"], "names": ["self", "_", "e", "messageSW", "sw", "data", "Promise", "resolve", "messageChannel", "MessageChannel", "port1", "onmessage", "event", "postMessage", "port2", "Deferred", "promise", "reject", "_this", "urlsMatch", "url1", "url2", "href", "location", "URL", "WorkboxEvent", "type", "props", "Object", "assign", "this", "_await", "value", "then", "direct", "_empty", "SKIP_WAITING_MESSAGE", "_awaitIgnored", "Workbox", "scriptURL", "registerOptions", "f", "_registerOptions", "_updateFoundCount", "_swDeferred", "_activeD<PERSON><PERSON>red", "_controlling<PERSON><PERSON><PERSON><PERSON>", "_registrationTime", "_ownSWs", "Set", "_onUpdateFound", "registration", "_registration", "installingSW", "installing", "_scriptURL", "toString", "performance", "now", "_externalSW", "removeEventListener", "_sw", "add", "addEventListener", "_onStateChange", "originalEvent", "target", "state", "isExternal", "eventProps", "_isUpdate", "isUpdate", "dispatchEvent", "_waitingTimeout", "setTimeout", "waiting", "clearTimeout", "_onControllerChange", "navigator", "serviceWorker", "controller", "_onMessage", "ports", "source", "getSW", "has", "args", "i", "arguments", "length", "apply", "register", "immediate", "body", "result", "document", "readyState", "res", "window", "Boolean", "_compatibleControllingSW", "_this3", "_getControllingSWIfCompatible", "_registerScript", "once", "waitingSW", "wasWaitingBeforeRegister", "update", "undefined", "messageSkipWaiting", "recover", "_this9", "reg", "error", "_eventListenerRegistry", "Map", "listener", "_getEventListenersByType", "delete", "set", "get"], "mappings": "AAEA,IACIA,KAAK,yBAA2BC,GACnC,CACD,MAAOC,ICmBP,SAASC,EAAUC,EAAIC,UACZ,IAAIC,SAAQ,SAACC,OACVC,EAAiB,IAAIC,eAC3BD,EAAeE,MAAMC,UAAY,SAACC,GAC9BL,EAAQK,EAAMP,OAElBD,EAAGS,YAAYR,EAAM,CAACG,EAAeM,UAE5C,49BC9BD,IACId,KAAK,uBAAyBC,GACjC,CACD,MAAOC,QCWDa,EAIF,2BACSC,QAAU,IAAIV,SAAQ,SAACC,EAASU,GACjCC,EAAKX,QAAUA,EACfW,EAAKD,OAASA,IAErB,ECRE,SAASE,EAAUC,EAAMC,OACpBC,EAASC,SAATD,YACD,IAAIE,IAAIJ,EAAME,GAAMA,OAAS,IAAIE,IAAIH,EAAMC,GAAMA,IAC3D,KCNYG,EACT,SAAYC,EAAMC,QACTD,KAAOA,EACZE,OAAOC,OAAOC,KAAMH,EACvB,ECkEE,SAASI,EAAOC,EAAOC,EAAMC,UAC/BA,EACID,EAAOA,EAAKD,GAASA,GAExBA,GAAUA,EAAMC,OACpBD,EAAQ1B,QAAQC,QAAQyB,IAElBC,EAAOD,EAAMC,KAAKA,GAAQD,EACjC,CAogBM,SAASG,KAxkBhB,IAAMC,EAAuB,CAAEV,KAAM,gBAuE9B,SAASW,EAAcL,EAAOE,OAC/BA,SACGF,GAASA,EAAMC,KAAOD,EAAMC,KAAKE,GAAU7B,QAAQC,SAE3D,KA9DK+B,iCAaUC,EAAWC,SAoBJC,kBApBID,IAAAA,EAAkB,2BAEhCE,GAAmB,KACnBC,GAAoB,IAEpBC,GAAc,IAAI7B,IAClB8B,GAAkB,IAAI9B,IACtB+B,GAAuB,IAAI/B,IAC3BgC,GAAoB,IACpBC,GAAU,IAAIC,MAIdC,GAAiB,eAEZC,EAAejC,EAAKkC,GACpBC,EAAeF,EAAaG,aAkB7BX,GAAoB,IAIpBxB,EAAUkC,EAAad,UAAWrB,EAAKqC,GAAWC,aAInDC,YAAYC,MAAQxC,EAAK6B,GAvEH,OA6EjBY,GAAcN,EACnBF,EAAaS,oBAAoB,cAAe1C,EAAKgC,QAKhDW,GAAMR,IACNL,GAAQc,IAAIT,KACZT,GAAYrC,QAAQ8C,MAc3BnC,EAAKyB,GAGPU,EAAaU,iBAAiB,cAAe7C,EAAK8C,OAMjDA,GAAiB,SAACC,OAEbd,EAAejC,EAAKkC,GACpBhD,EAAK6D,EAAcC,OACjBC,EAAU/D,EAAV+D,MACFC,EAAahE,IAAOc,EAAKyC,GACzBU,EAAa,CACfjE,GAAAA,EACAgE,WAAAA,EACAH,cAAAA,IAECG,GAAclD,EAAKoD,KACpBD,EAAWE,UAAW,KAErBC,cAAc,IAAI/C,EAAa0C,EAAOE,IAC7B,cAAVF,IASKM,GAAkBzE,KAAK0E,YAAW,WAErB,cAAVP,GAAyBhB,EAAawB,UAAYvE,KAC7CoE,cAAc,IAAI/C,EAAa,UAAW4C,MA1ItC,KAwJF,eAAVF,IACLS,aAAa1D,EAAKuD,IACbL,KACIvB,GAAgBtC,QAAQH,OA0CpCyE,GAAsB,SAACZ,OAClB7D,EAAKc,EAAK2C,GACVO,EAAahE,IAAO0E,UAAUC,cAAcC,aAK7CR,cAAc,IAAI/C,EAAa,cAAe,CAC/C2C,WAAAA,EACAH,cAAAA,EACA7D,GAAAA,EACAmE,SAAUrD,EAAKoD,MAEdF,KAIItB,GAAqBvC,QAAQH,MAOrC6E,IAzKUxC,WAyKUwB,OAGb5D,EAAwB4D,EAAxB5D,KAAM6E,EAAkBjB,EAAlBiB,MAAOC,EAAWlB,EAAXkB,gBAGfjE,EAAKkE,oBAOPlE,EAAK8B,GAAQqC,IAAIF,MACZX,cAAc,IAAI/C,EAAa,UAAW,CAG3CpB,KAAAA,EACA4D,cAAAA,EACAiB,MAAAA,EACA9E,GAAI+E,SA5LhB,eACD,IAAIG,EAAO,GAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAChDD,EAAKC,GAAKC,UAAUD,cAGbjF,QAAQC,QAAQkC,EAAEiD,MAAM5D,KAAMwD,IACpC,MAAMpF,UACAI,QAAQW,OAAOf,QAyLZqD,GAAahB,IACbG,GAAmBF,EAIxBsC,UAAUC,cAAchB,iBAAiB,UAAW7C,EAAK+D,8HAYvDU,uCAAiC,MAAtBC,UAAAA,0BAEL9D,YAuQT,SAAiB+D,EAAM5D,OACzB6D,EAASD,OACTC,GAAUA,EAAO7D,YACb6D,EAAO7D,KAAKA,UAEbA,EAAK6D,EACZ,kBAvQYF,GAAqC,aAAxBG,SAASC,oBACjB,IAAI1F,SAAQ,SAAC2F,UAAQC,OAAOnC,iBAAiB,OAAQkC,8BAI1D3B,GAAY6B,QAAQrB,UAAUC,cAAcC,cAI5CoB,GAA2BC,EAAKC,OACVD,EAAKE,oBAA3BnD,KAGDiD,EAAKD,OACAvC,GAAMwC,EAAKD,KACXvD,GAAgBtC,QAAQ8F,EAAKD,MAC7BtD,GAAqBvC,QAAQ8F,EAAKD,MAClCA,GAAyBrC,iBAAiB,cAAesC,EAAKrC,GAAgB,CAAEwC,MAAM,SAOzFC,EAAYJ,EAAKjD,GAAcuB,eACjC8B,GACAtF,EAAUsF,EAAUlE,UAAW8D,EAAK9C,GAAWC,gBAG1CK,GAAM4C,EAGCnG,QAAQC,UAAU0B,MAAK,aAC1BuC,cAAc,IAAI/C,EAAa,UAAW,CAC3CrB,GAAIqG,EACJC,0BAA0B,QCrT7BzE,MAAK,gBD8TVoE,EAAKxC,OACAjB,GAAYrC,QAAQ8F,EAAKxC,MACzBb,GAAQc,IAAIuC,EAAKxC,OAyBrBT,GAAcW,iBAAiB,cAAesC,EAAKnD,IACxD4B,UAAUC,cAAchB,iBAAiB,mBAAoBsC,EAAKxB,IAC3DwB,EAAKjD,+CAKVuD,6BACG7E,KAAKsB,KAALtB,KAQMsB,GAAcuD,uDA4C7BvB,MAAA,uBAGwBwB,IAAb9E,KAAK+B,GACNvD,QAAQC,QAAQuB,KAAK+B,IACrB/B,KAAKc,GAAY5B,WAiBrBb,mBAAUE,gBACKyB,KAAKsD,kBAAhBhF,UACCD,EAAUC,EAAIC,4CASzBwG,mBAAA,WACQ/E,KAAKsB,IAAiBtB,KAAKsB,GAAcuB,SACpCxE,EAAU2B,KAAKsB,GAAcuB,QAASvC,MAUnDkE,GAAA,eACUtB,EAAaF,UAAUC,cAAcC,kBACvCA,GACA7D,EAAU6D,EAAWzC,UAAWT,KAAKyB,GAAWC,YACzCwB,YAYTuB,wBAKqDzE,YA6ExD,SAAgB+D,EAAMiB,WAEvBhB,EAASD,IACZ,MAAM3F,UACA4G,EAAQ5G,MAEZ4F,GAAUA,EAAO7D,YACb6D,EAAO7D,UAAK,EAAQ6E,UAErBhB,CACP,uBAvF6BhB,UAAUC,cAAcY,SAASoB,EAAKxD,GAAYwD,EAAKrE,cAAnEsE,YAIDjE,GAAoBU,YAAYC,MAC9BsD,iBAEJC,SAKGA,qFA9GHnF,KAAKe,GAAgB7B,mDAerBc,KAAKgB,GAAqB9B,wEEnY5BkG,GAAyB,IAAIC,+BAOtCpD,iBAAA,SAAiBrC,EAAM0F,GACPtF,KAAKuF,GAAyB3F,GACtCoC,IAAIsD,MAOZxD,oBAAA,SAAoBlC,EAAM0F,QACjBC,GAAyB3F,GAAM4F,OAAOF,MAM/C5C,cAAA,SAAc5D,GACVA,EAAMsD,OAASpC,mBACGA,KAAKuF,GAAyBzG,EAAMc,sBACpB,EAC9B0F,WAASxG,OAWjByG,GAAA,SAAyB3F,UAChBI,KAAKoF,GAAuB7B,IAAI3D,SAC5BwF,GAAuBK,IAAI7F,EAAM,IAAIuB,KAEvCnB,KAAKoF,GAAuBM,IAAI9F,KA5C/C"}