# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.2.0](https://github.com/inspect-js/has-proto/compare/v1.1.0...v1.2.0) - 2024-12-06

### Commits

- [Refactor] use `dunder-proto` instead of `call-bind` [`6e5e76c`](https://github.com/inspect-js/has-proto/commit/6e5e76ce3bf4c01ebb99b38dcd61bee1ba35073f)
- [actions] split out node 10-20, and 20+ [`3b8e9e6`](https://github.com/inspect-js/has-proto/commit/3b8e9e651431ef5e9725dae68881b8107e812ee0)
- [<PERSON>] update `@ljharb/tsconfig`, `gopd` [`57bcd00`](https://github.com/inspect-js/has-proto/commit/57bcd000625c7d1e7f41fd10b4a8e1ea380029dd)
- [actions] skip `npm ls` in node &lt; 10 [`ce3a4d7`](https://github.com/inspect-js/has-proto/commit/ce3a4d76d4f15d94540cb5f2ae50967cc6299ee3)

## [v1.1.0](https://github.com/inspect-js/has-proto/compare/v1.0.3...v1.1.0) - 2024-12-01

### Commits

- [New] add `accessor` and `mutator` endpoints [`144f6a9`](https://github.com/inspect-js/has-proto/commit/144f6a9c2a3925f25058d5d5ea7eab3be57767d9)
- [types] use shared config [`8b597cf`](https://github.com/inspect-js/has-proto/commit/8b597cff2b09f0351bc357cac0e0c7b0c8bb7e70)
- [Refactor] cache result at module level [`88418bd`](https://github.com/inspect-js/has-proto/commit/88418bde7e0c37c7d9aa6cc79150e774004c01d8)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `tape` [`d246200`](https://github.com/inspect-js/has-proto/commit/d246200bae6ceceebb495df7f8eb0f27a017b63f)
- [Deps] update `gopd`, `reflect.getprototypeof` [`6f72364`](https://github.com/inspect-js/has-proto/commit/6f723645da9b5bef0aaae4a1aa66c07a1fed179f)
- [Tests] add `@arethetypeswrong/cli` [`8194e1a`](https://github.com/inspect-js/has-proto/commit/8194e1a607233f63c5bd0b91112c0423b3296ac9)
- [Tests] replace `aud` with `npm audit` [`fd7ad11`](https://github.com/inspect-js/has-proto/commit/fd7ad111dc35488b3200a763204dba0f6087defc)
- [Dev Deps] update `@types/tape` [`2695808`](https://github.com/inspect-js/has-proto/commit/26958086aec0b1cbfdddd4f10e54d2de1facf85c)
- [Dev Deps] add missing peer dep [`fa4b2f7`](https://github.com/inspect-js/has-proto/commit/fa4b2f77f7c0071e1c06b5590c9bada8e6b2edce)

## [v1.0.3](https://github.com/inspect-js/has-proto/compare/v1.0.2...v1.0.3) - 2024-02-19

### Commits

- [types] add missing declaration file [`26ecade`](https://github.com/inspect-js/has-proto/commit/26ecade05d253bb5dc376945ee3186d1fbe334f8)

## [v1.0.2](https://github.com/inspect-js/has-proto/compare/v1.0.1...v1.0.2) - 2024-02-19

### Commits

- add types [`6435262`](https://github.com/inspect-js/has-proto/commit/64352626cf511c0276d5f4bb6be770a0bf0f8524)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `npmignore`, `tape` [`f16a5e4`](https://github.com/inspect-js/has-proto/commit/f16a5e4121651e551271419f9d60fdd3561fd82c)
- [Refactor] tiny cleanup [`d1f1a4b`](https://github.com/inspect-js/has-proto/commit/d1f1a4bdc135f115a10f148ce302676224534702)
- [meta] add `sideEffects` flag [`e7ab1a6`](https://github.com/inspect-js/has-proto/commit/e7ab1a6f153b3e80dee68d1748b71e46767a0531)

## [v1.0.1](https://github.com/inspect-js/has-proto/compare/v1.0.0...v1.0.1) - 2022-12-21

### Commits

- [meta] correct URLs and description [`ef34483`](https://github.com/inspect-js/has-proto/commit/ef34483ca0d35680f271b6b96e35526151b25dfc)
- [patch] add an additional criteria [`e81959e`](https://github.com/inspect-js/has-proto/commit/e81959ed7c7a77fbf459f00cb4ef824f1099497f)
- [Dev Deps] update `aud` [`2bec2c4`](https://github.com/inspect-js/has-proto/commit/2bec2c47b072b122ff5443fba0263f6dc649531f)

## v1.0.0 - 2022-12-12

### Commits

- Initial implementation, tests, readme [`6886fea`](https://github.com/inspect-js/has-proto/commit/6886fea578f67daf69a7920b2eb7637ea6ebb0bc)
- Initial commit [`99129c8`](https://github.com/inspect-js/has-proto/commit/99129c8f42471ac89cb681ba9cb9d52a583eb94f)
- npm init [`2844ad8`](https://github.com/inspect-js/has-proto/commit/2844ad8e75b84d66a46765b3bab9d2e8ea692e10)
- Only apps should have lockfiles [`c65bc5e`](https://github.com/inspect-js/has-proto/commit/c65bc5e40b9004463f7336d47c67245fb139a36a)
