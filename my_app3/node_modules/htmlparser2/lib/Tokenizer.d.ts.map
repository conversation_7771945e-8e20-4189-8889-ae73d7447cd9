{"version": 3, "file": "Tokenizer.d.ts", "sourceRoot": "", "sources": ["../src/Tokenizer.ts"], "names": [], "mappings": "AAKA,8CAA8C;AAC9C,mBAAW,KAAK;IACZ,IAAI,IAAI;IACR,aAAa,IAAA;IACb,SAAS,IAAA;IACT,gBAAgB,IAAA;IAChB,oBAAoB,IAAA;IACpB,gBAAgB,IAAA;IAChB,mBAAmB,IAAA;IAGnB,mBAAmB,IAAA;IACnB,eAAe,IAAA;IACf,kBAAkB,KAAA;IAClB,oBAAoB,KAAA;IACpB,kBAAkB,KAAA;IAClB,kBAAkB,KAAA;IAClB,kBAAkB,KAAA;IAGlB,iBAAiB,KAAA;IACjB,aAAa,KAAA;IAGb,uBAAuB,KAAA;IAGvB,aAAa,KAAA;IACb,SAAS,KAAA;IACT,gBAAgB,KAAA;IAChB,aAAa,KAAA;IACb,aAAa,KAAA;IAGb,YAAY,KAAA;IACZ,YAAY,KAAA;IACZ,YAAY,KAAA;IACZ,YAAY,KAAA;IACZ,YAAY,KAAA;IACZ,YAAY,KAAA;IACZ,OAAO,KAAA;IACP,WAAW,KAAA;IACX,WAAW,KAAA;IAGX,cAAc,KAAA;IACd,iBAAiB,KAAA;IAEjB,aAAa,KAAA;IACb,aAAa,KAAA;IACb,aAAa,KAAA;IACb,aAAa,KAAA;IACb,aAAa,KAAA;IACb,YAAY,KAAA;IACZ,YAAY,KAAA;IACZ,YAAY,KAAA;IACZ,YAAY,KAAA;IACZ,YAAY,KAAA;IAEZ,YAAY,KAAA;IACZ,YAAY,KAAA;IACZ,YAAY,KAAA;IACZ,YAAY,KAAA;IACZ,WAAW,KAAA;IACX,WAAW,KAAA;IACX,WAAW,KAAA;IACX,WAAW,KAAA;IAEX,cAAc,KAAA;IACd,iBAAiB,KAAA;IACjB,YAAY,KAAA;IACZ,YAAY,KAAA;IACZ,YAAY,KAAA;IACZ,YAAY,KAAA;IACZ,WAAW,KAAA;IACX,WAAW,KAAA;IACX,WAAW,KAAA;IACX,WAAW,KAAA;IAEX,YAAY,KAAA;IACZ,mBAAmB,KAAA;IACnB,aAAa,KAAA;IACb,eAAe,KAAA;IACf,WAAW,KAAA;CACd;AAiBD,MAAM,WAAW,SAAS;IACtB,YAAY,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;IAClC,WAAW,CAAC,KAAK,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC;IACpD,YAAY,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IACjC,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IAC5B,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IAC/B,SAAS,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IAC9B,aAAa,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,CAAC;IACrC,KAAK,IAAI,IAAI,CAAC;IACd,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC;IAC3C,YAAY,IAAI,IAAI,CAAC;IACrB,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IAClC,uBAAuB,CAAC,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;IACnD,gBAAgB,IAAI,IAAI,CAAC;IACzB,MAAM,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;CAC/B;AAuGD,MAAM,CAAC,OAAO,OAAO,SAAS;IAC1B,6CAA6C;IAC7C,MAAM,QAAc;IACpB,uBAAuB;IACvB,OAAO,CAAC,MAAM,CAAM;IACpB,iEAAiE;IAC1D,YAAY,SAAK;IACxB,oEAAoE;IACpE,MAAM,SAAK;IACX;;;OAGG;IACH,OAAO,CAAC,YAAY,CAAK;IACzB,kIAAkI;IAClI,OAAO,CAAC,SAAS,CAAc;IAC/B,oEAAoE;IACpE,OAAO,CAAC,OAAO,CAAgB;IAC/B,uDAAuD;IACvD,OAAO,CAAC,OAAO,CAAQ;IACvB,qFAAqF;IACrF,OAAO,CAAC,KAAK,CAAS;IAEtB,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAY;IAChC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAU;IAClC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAU;gBAGrC,OAAO,EAAE;QAAE,OAAO,CAAC,EAAE,OAAO,CAAC;QAAC,cAAc,CAAC,EAAE,OAAO,CAAA;KAAE,GAAG,IAAI,EAC/D,GAAG,EAAE,SAAS;IAOX,KAAK,IAAI,IAAI;IAYb,KAAK,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAM1B,GAAG,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI;IAOzB,KAAK,IAAI,IAAI;IAIb,MAAM,IAAI,IAAI;IAUrB;;OAEG;IACI,gBAAgB,IAAI,MAAM;IAIjC,OAAO,CAAC,SAAS;IAoBjB;;;;;OAKG;IACH,OAAO,CAAC,cAAc;IAMtB,OAAO,CAAC,kBAAkB;IA8B1B,OAAO,CAAC,cAAc;IAOtB,OAAO,CAAC,yBAAyB;IAyBjC,OAAO,CAAC,qBAAqB;IAO7B,OAAO,CAAC,wBAAwB;IAOhC,OAAO,CAAC,wBAAwB;IAYhC,OAAO,CAAC,qBAAqB;IAW7B,OAAO,CAAC,oBAAoB;IAQ5B,OAAO,CAAC,uBAAuB;IAa/B,OAAO,CAAC,yBAAyB;IAajC,OAAO,CAAC,sBAAsB;IAY9B,OAAO,CAAC,iCAAiC;IAGzC,OAAO,CAAC,iCAAiC;IAGzC,OAAO,CAAC,6BAA6B;IAarC,OAAO,CAAC,sBAAsB;IAQ9B,OAAO,CAAC,kBAAkB;IAO1B,OAAO,CAAC,4BAA4B;IAOpC,OAAO,CAAC,kBAAkB;IAQ1B,OAAO,CAAC,cAAc;IAGtB,OAAO,CAAC,qBAAqB;IAS7B,OAAO,CAAC,kBAAkB;IAO1B,OAAO,CAAC,kBAAkB;IAa1B,OAAO,CAAC,iBAAiB;IASzB,OAAO,CAAC,YAAY;IAGpB,OAAO,CAAC,gBAAgB;IAIxB,OAAO,CAAC,gBAAgB;IAaxB,OAAO,CAAC,mBAAmB;IAU3B,OAAO,CAAC,sBAAsB;IAO9B,OAAO,CAAC,sBAAsB;IAO9B,OAAO,CAAC,qBAAqB;IAS7B,OAAO,CAAC,gBAAgB;IAgBxB,OAAO,CAAC,iBAAiB;IAezB,OAAO,CAAC,kBAAkB;IA2B1B,OAAO,CAAC,mBAAmB;IAW3B,OAAO,CAAC,oBAAoB;IAY5B,OAAO,CAAC,gBAAgB;IAiBxB,OAAO,CAAC,OAAO;IA4Bf;;;;OAIG;IACH,OAAO,CAAC,KAAK;IAgJb,OAAO,CAAC,MAAM;IAQd,OAAO,CAAC,kBAAkB;IAmD1B,OAAO,CAAC,UAAU;IAGlB,OAAO,CAAC,SAAS;IAIjB,OAAO,CAAC,WAAW;CAOtB"}