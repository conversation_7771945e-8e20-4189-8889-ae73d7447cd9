{"name": "workbox-sw", "version": "6.6.1", "license": "MIT", "author": "Google's Web DevRel Team", "description": "This module makes it easy to get started with the Workbox service worker libraries.", "repository": "googlechrome/workbox", "bugs": "https://github.com/googlechrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "keywords": ["workbox", "workboxjs", "service worker", "sw"], "workbox": {"browserNamespace": "workbox", "packageType": "sw", "prodOnly": true}, "main": "build/workbox-sw.js", "module": "index.mjs", "gitHead": "4a3c20d121efd878f3982760620f640e5bd67250"}