{"name": "harmony-reflect", "version": "1.6.2", "description": "ES5 shim for ES6 (ECMAScript 6) Reflect and Proxy objects", "main": "reflect.js", "keywords": ["reflection", "proxies", "proxy", "reflect", "harmony", "es6"], "license": "(Apache-2.0 OR MPL-1.1)", "homepage": "https://github.com/tvcutsem/harmony-reflect", "typings": "index.d.ts", "repository": {"type": "git", "url": "https://<EMAIL>/tvcutsem/harmony-reflect.git"}}