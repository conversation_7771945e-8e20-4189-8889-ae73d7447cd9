{"version": 3, "file": "workbox-range-requests.dev.js", "sources": ["../_version.js", "../utils/calculateEffectiveBoundaries.js", "../utils/parseRangeHeader.js", "../createPartialResponse.js", "../RangeRequestsPlugin.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:range-requests:6.6.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { assert } from 'workbox-core/_private/assert.js';\nimport '../_version.js';\n/**\n * @param {Blob} blob A source blob.\n * @param {number} [start] The offset to use as the start of the\n * slice.\n * @param {number} [end] The offset to use as the end of the slice.\n * @return {Object} An object with `start` and `end` properties, reflecting\n * the effective boundaries to use given the size of the blob.\n *\n * @private\n */\nfunction calculateEffectiveBoundaries(blob, start, end) {\n    if (process.env.NODE_ENV !== 'production') {\n        assert.isInstance(blob, Blob, {\n            moduleName: 'workbox-range-requests',\n            funcName: 'calculateEffectiveBoundaries',\n            paramName: 'blob',\n        });\n    }\n    const blobSize = blob.size;\n    if ((end && end > blobSize) || (start && start < 0)) {\n        throw new WorkboxError('range-not-satisfiable', {\n            size: blobSize,\n            end,\n            start,\n        });\n    }\n    let effectiveStart;\n    let effectiveEnd;\n    if (start !== undefined && end !== undefined) {\n        effectiveStart = start;\n        // Range values are inclusive, so add 1 to the value.\n        effectiveEnd = end + 1;\n    }\n    else if (start !== undefined && end === undefined) {\n        effectiveStart = start;\n        effectiveEnd = blobSize;\n    }\n    else if (end !== undefined && start === undefined) {\n        effectiveStart = blobSize - end;\n        effectiveEnd = blobSize;\n    }\n    return {\n        start: effectiveStart,\n        end: effectiveEnd,\n    };\n}\nexport { calculateEffectiveBoundaries };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { assert } from 'workbox-core/_private/assert.js';\nimport '../_version.js';\n/**\n * @param {string} rangeHeader A Range: header value.\n * @return {Object} An object with `start` and `end` properties, reflecting\n * the parsed value of the Range: header. If either the `start` or `end` are\n * omitted, then `null` will be returned.\n *\n * @private\n */\nfunction parseRangeHeader(rangeHeader) {\n    if (process.env.NODE_ENV !== 'production') {\n        assert.isType(rangeHeader, 'string', {\n            moduleName: 'workbox-range-requests',\n            funcName: 'parseRangeHeader',\n            paramName: 'rangeHeader',\n        });\n    }\n    const normalizedRangeHeader = rangeHeader.trim().toLowerCase();\n    if (!normalizedRangeHeader.startsWith('bytes=')) {\n        throw new WorkboxError('unit-must-be-bytes', { normalizedRangeHeader });\n    }\n    // Specifying multiple ranges separate by commas is valid syntax, but this\n    // library only attempts to handle a single, contiguous sequence of bytes.\n    // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Range#Syntax\n    if (normalizedRangeHeader.includes(',')) {\n        throw new WorkboxError('single-range-only', { normalizedRangeHeader });\n    }\n    const rangeParts = /(\\d*)-(\\d*)/.exec(normalizedRangeHeader);\n    // We need either at least one of the start or end values.\n    if (!rangeParts || !(rangeParts[1] || rangeParts[2])) {\n        throw new WorkboxError('invalid-range-values', { normalizedRangeHeader });\n    }\n    return {\n        start: rangeParts[1] === '' ? undefined : Number(rangeParts[1]),\n        end: rangeParts[2] === '' ? undefined : Number(rangeParts[2]),\n    };\n}\nexport { parseRangeHeader };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { calculateEffectiveBoundaries } from './utils/calculateEffectiveBoundaries.js';\nimport { parseRangeHeader } from './utils/parseRangeHeader.js';\nimport './_version.js';\n/**\n * Given a `Request` and `Response` objects as input, this will return a\n * promise for a new `Response`.\n *\n * If the original `Response` already contains partial content (i.e. it has\n * a status of 206), then this assumes it already fulfills the `Range:`\n * requirements, and will return it as-is.\n *\n * @param {Request} request A request, which should contain a Range:\n * header.\n * @param {Response} originalResponse A response.\n * @return {Promise<Response>} Either a `206 Partial Content` response, with\n * the response body set to the slice of content specified by the request's\n * `Range:` header, or a `416 Range Not Satisfiable` response if the\n * conditions of the `Range:` header can't be met.\n *\n * @memberof workbox-range-requests\n */\nasync function createPartialResponse(request, originalResponse) {\n    try {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-range-requests',\n                funcName: 'createPartialResponse',\n                paramName: 'request',\n            });\n            assert.isInstance(originalResponse, Response, {\n                moduleName: 'workbox-range-requests',\n                funcName: 'createPartialResponse',\n                paramName: 'originalResponse',\n            });\n        }\n        if (originalResponse.status === 206) {\n            // If we already have a 206, then just pass it through as-is;\n            // see https://github.com/GoogleChrome/workbox/issues/1720\n            return originalResponse;\n        }\n        const rangeHeader = request.headers.get('range');\n        if (!rangeHeader) {\n            throw new WorkboxError('no-range-header');\n        }\n        const boundaries = parseRangeHeader(rangeHeader);\n        const originalBlob = await originalResponse.blob();\n        const effectiveBoundaries = calculateEffectiveBoundaries(originalBlob, boundaries.start, boundaries.end);\n        const slicedBlob = originalBlob.slice(effectiveBoundaries.start, effectiveBoundaries.end);\n        const slicedBlobSize = slicedBlob.size;\n        const slicedResponse = new Response(slicedBlob, {\n            // Status code 206 is for a Partial Content response.\n            // See https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/206\n            status: 206,\n            statusText: 'Partial Content',\n            headers: originalResponse.headers,\n        });\n        slicedResponse.headers.set('Content-Length', String(slicedBlobSize));\n        slicedResponse.headers.set('Content-Range', `bytes ${effectiveBoundaries.start}-${effectiveBoundaries.end - 1}/` +\n            `${originalBlob.size}`);\n        return slicedResponse;\n    }\n    catch (error) {\n        if (process.env.NODE_ENV !== 'production') {\n            logger.warn(`Unable to construct a partial response; returning a ` +\n                `416 Range Not Satisfiable response instead.`);\n            logger.groupCollapsed(`View details here.`);\n            logger.log(error);\n            logger.log(request);\n            logger.log(originalResponse);\n            logger.groupEnd();\n        }\n        return new Response('', {\n            status: 416,\n            statusText: 'Range Not Satisfiable',\n        });\n    }\n}\nexport { createPartialResponse };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { createPartialResponse } from './createPartialResponse.js';\nimport './_version.js';\n/**\n * The range request plugin makes it easy for a request with a 'Range' header to\n * be fulfilled by a cached response.\n *\n * It does this by intercepting the `cachedResponseWillBeUsed` plugin callback\n * and returning the appropriate subset of the cached response body.\n *\n * @memberof workbox-range-requests\n */\nclass RangeRequestsPlugin {\n    constructor() {\n        /**\n         * @param {Object} options\n         * @param {Request} options.request The original request, which may or may not\n         * contain a Range: header.\n         * @param {Response} options.cachedResponse The complete cached response.\n         * @return {Promise<Response>} If request contains a 'Range' header, then a\n         * new response with status 206 whose body is a subset of `cachedResponse` is\n         * returned. Otherwise, `cachedResponse` is returned as-is.\n         *\n         * @private\n         */\n        this.cachedResponseWillBeUsed = async ({ request, cachedResponse, }) => {\n            // Only return a sliced response if there's something valid in the cache,\n            // and there's a Range: header in the request.\n            if (cachedResponse && request.headers.has('range')) {\n                return await createPartialResponse(request, cachedResponse);\n            }\n            // If there was no Range: header, or if cachedResponse wasn't valid, just\n            // pass it through as-is.\n            return cachedResponse;\n        };\n    }\n}\nexport { RangeRequestsPlugin };\n"], "names": ["self", "_", "e", "calculateEffectiveBoundaries", "blob", "start", "end", "assert", "isInstance", "Blob", "moduleName", "funcName", "paramName", "blobSize", "size", "WorkboxError", "effectiveStart", "effectiveEnd", "undefined", "parseRange<PERSON><PERSON>er", "rangeHeader", "isType", "normalizedRangeHeader", "trim", "toLowerCase", "startsWith", "includes", "rangeParts", "exec", "Number", "createPartialResponse", "request", "originalResponse", "process", "Request", "Response", "status", "headers", "get", "boundaries", "originalBlob", "effectiveBoundaries", "slicedBlob", "slice", "slicedBlobSize", "slicedResponse", "statusText", "set", "String", "error", "logger", "warn", "groupCollapsed", "log", "groupEnd", "RangeRequestsPlugin", "constructor", "cachedResponseWillBeUsed", "cachedResponse", "has"], "mappings": ";;;;IAEA,IAAI;IACAA,EAAAA,IAAI,CAAC,8BAAD,CAAJ,IAAwCC,CAAC,EAAzC;IACH,CAFD,CAGA,OAAOC,CAAP,EAAU;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASC,4BAAT,CAAsCC,IAAtC,EAA4CC,KAA5C,EAAmDC,GAAnD,EAAwD;IACpD,EAA2C;IACvCC,IAAAA,gBAAM,CAACC,UAAP,CAAkBJ,IAAlB,EAAwBK,IAAxB,EAA8B;IAC1BC,MAAAA,UAAU,EAAE,wBADc;IAE1BC,MAAAA,QAAQ,EAAE,8BAFgB;IAG1BC,MAAAA,SAAS,EAAE;IAHe,KAA9B;IAKH;;IACD,QAAMC,QAAQ,GAAGT,IAAI,CAACU,IAAtB;;IACA,MAAKR,GAAG,IAAIA,GAAG,GAAGO,QAAd,IAA4BR,KAAK,IAAIA,KAAK,GAAG,CAAjD,EAAqD;IACjD,UAAM,IAAIU,4BAAJ,CAAiB,uBAAjB,EAA0C;IAC5CD,MAAAA,IAAI,EAAED,QADsC;IAE5CP,MAAAA,GAF4C;IAG5CD,MAAAA;IAH4C,KAA1C,CAAN;IAKH;;IACD,MAAIW,cAAJ;IACA,MAAIC,YAAJ;;IACA,MAAIZ,KAAK,KAAKa,SAAV,IAAuBZ,GAAG,KAAKY,SAAnC,EAA8C;IAC1CF,IAAAA,cAAc,GAAGX,KAAjB,CAD0C;;IAG1CY,IAAAA,YAAY,GAAGX,GAAG,GAAG,CAArB;IACH,GAJD,MAKK,IAAID,KAAK,KAAKa,SAAV,IAAuBZ,GAAG,KAAKY,SAAnC,EAA8C;IAC/CF,IAAAA,cAAc,GAAGX,KAAjB;IACAY,IAAAA,YAAY,GAAGJ,QAAf;IACH,GAHI,MAIA,IAAIP,GAAG,KAAKY,SAAR,IAAqBb,KAAK,KAAKa,SAAnC,EAA8C;IAC/CF,IAAAA,cAAc,GAAGH,QAAQ,GAAGP,GAA5B;IACAW,IAAAA,YAAY,GAAGJ,QAAf;IACH;;IACD,SAAO;IACHR,IAAAA,KAAK,EAAEW,cADJ;IAEHV,IAAAA,GAAG,EAAEW;IAFF,GAAP;IAIH;;ICvDD;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASE,gBAAT,CAA0BC,WAA1B,EAAuC;IACnC,EAA2C;IACvCb,IAAAA,gBAAM,CAACc,MAAP,CAAcD,WAAd,EAA2B,QAA3B,EAAqC;IACjCV,MAAAA,UAAU,EAAE,wBADqB;IAEjCC,MAAAA,QAAQ,EAAE,kBAFuB;IAGjCC,MAAAA,SAAS,EAAE;IAHsB,KAArC;IAKH;;IACD,QAAMU,qBAAqB,GAAGF,WAAW,CAACG,IAAZ,GAAmBC,WAAnB,EAA9B;;IACA,MAAI,CAACF,qBAAqB,CAACG,UAAtB,CAAiC,QAAjC,CAAL,EAAiD;IAC7C,UAAM,IAAIV,4BAAJ,CAAiB,oBAAjB,EAAuC;IAAEO,MAAAA;IAAF,KAAvC,CAAN;IACH,GAXkC;IAanC;IACA;;;IACA,MAAIA,qBAAqB,CAACI,QAAtB,CAA+B,GAA/B,CAAJ,EAAyC;IACrC,UAAM,IAAIX,4BAAJ,CAAiB,mBAAjB,EAAsC;IAAEO,MAAAA;IAAF,KAAtC,CAAN;IACH;;IACD,QAAMK,UAAU,GAAG,cAAcC,IAAd,CAAmBN,qBAAnB,CAAnB,CAlBmC;;IAoBnC,MAAI,CAACK,UAAD,IAAe,EAAEA,UAAU,CAAC,CAAD,CAAV,IAAiBA,UAAU,CAAC,CAAD,CAA7B,CAAnB,EAAsD;IAClD,UAAM,IAAIZ,4BAAJ,CAAiB,sBAAjB,EAAyC;IAAEO,MAAAA;IAAF,KAAzC,CAAN;IACH;;IACD,SAAO;IACHjB,IAAAA,KAAK,EAAEsB,UAAU,CAAC,CAAD,CAAV,KAAkB,EAAlB,GAAuBT,SAAvB,GAAmCW,MAAM,CAACF,UAAU,CAAC,CAAD,CAAX,CAD7C;IAEHrB,IAAAA,GAAG,EAAEqB,UAAU,CAAC,CAAD,CAAV,KAAkB,EAAlB,GAAuBT,SAAvB,GAAmCW,MAAM,CAACF,UAAU,CAAC,CAAD,CAAX;IAF3C,GAAP;IAIH;;IC7CD;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,eAAeG,qBAAf,CAAqCC,OAArC,EAA8CC,gBAA9C,EAAgE;IAC5D,MAAI;IACA,QAAIC,KAAA,KAAyB,YAA7B,EAA2C;IACvC1B,MAAAA,gBAAM,CAACC,UAAP,CAAkBuB,OAAlB,EAA2BG,OAA3B,EAAoC;IAChCxB,QAAAA,UAAU,EAAE,wBADoB;IAEhCC,QAAAA,QAAQ,EAAE,uBAFsB;IAGhCC,QAAAA,SAAS,EAAE;IAHqB,OAApC;IAKAL,MAAAA,gBAAM,CAACC,UAAP,CAAkBwB,gBAAlB,EAAoCG,QAApC,EAA8C;IAC1CzB,QAAAA,UAAU,EAAE,wBAD8B;IAE1CC,QAAAA,QAAQ,EAAE,uBAFgC;IAG1CC,QAAAA,SAAS,EAAE;IAH+B,OAA9C;IAKH;;IACD,QAAIoB,gBAAgB,CAACI,MAAjB,KAA4B,GAAhC,EAAqC;IACjC;IACA;IACA,aAAOJ,gBAAP;IACH;;IACD,UAAMZ,WAAW,GAAGW,OAAO,CAACM,OAAR,CAAgBC,GAAhB,CAAoB,OAApB,CAApB;;IACA,QAAI,CAAClB,WAAL,EAAkB;IACd,YAAM,IAAIL,4BAAJ,CAAiB,iBAAjB,CAAN;IACH;;IACD,UAAMwB,UAAU,GAAGpB,gBAAgB,CAACC,WAAD,CAAnC;IACA,UAAMoB,YAAY,GAAG,MAAMR,gBAAgB,CAAC5B,IAAjB,EAA3B;IACA,UAAMqC,mBAAmB,GAAGtC,4BAA4B,CAACqC,YAAD,EAAeD,UAAU,CAAClC,KAA1B,EAAiCkC,UAAU,CAACjC,GAA5C,CAAxD;IACA,UAAMoC,UAAU,GAAGF,YAAY,CAACG,KAAb,CAAmBF,mBAAmB,CAACpC,KAAvC,EAA8CoC,mBAAmB,CAACnC,GAAlE,CAAnB;IACA,UAAMsC,cAAc,GAAGF,UAAU,CAAC5B,IAAlC;IACA,UAAM+B,cAAc,GAAG,IAAIV,QAAJ,CAAaO,UAAb,EAAyB;IAC5C;IACA;IACAN,MAAAA,MAAM,EAAE,GAHoC;IAI5CU,MAAAA,UAAU,EAAE,iBAJgC;IAK5CT,MAAAA,OAAO,EAAEL,gBAAgB,CAACK;IALkB,KAAzB,CAAvB;IAOAQ,IAAAA,cAAc,CAACR,OAAf,CAAuBU,GAAvB,CAA2B,gBAA3B,EAA6CC,MAAM,CAACJ,cAAD,CAAnD;IACAC,IAAAA,cAAc,CAACR,OAAf,CAAuBU,GAAvB,CAA2B,eAA3B,EAA6C,SAAQN,mBAAmB,CAACpC,KAAM,IAAGoC,mBAAmB,CAACnC,GAApB,GAA0B,CAAE,GAAlE,GACvC,GAAEkC,YAAY,CAAC1B,IAAK,EADzB;IAEA,WAAO+B,cAAP;IACH,GAtCD,CAuCA,OAAOI,KAAP,EAAc;IACV,IAA2C;IACvCC,MAAAA,gBAAM,CAACC,IAAP,CAAa,sDAAD,GACP,6CADL;IAEAD,MAAAA,gBAAM,CAACE,cAAP,CAAuB,oBAAvB;IACAF,MAAAA,gBAAM,CAACG,GAAP,CAAWJ,KAAX;IACAC,MAAAA,gBAAM,CAACG,GAAP,CAAWtB,OAAX;IACAmB,MAAAA,gBAAM,CAACG,GAAP,CAAWrB,gBAAX;IACAkB,MAAAA,gBAAM,CAACI,QAAP;IACH;;IACD,WAAO,IAAInB,QAAJ,CAAa,EAAb,EAAiB;IACpBC,MAAAA,MAAM,EAAE,GADY;IAEpBU,MAAAA,UAAU,EAAE;IAFQ,KAAjB,CAAP;IAIH;IACJ;;ICtFD;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMS,mBAAN,CAA0B;IACtBC,EAAAA,WAAW,GAAG;IACV;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACQ,SAAKC,wBAAL,GAAgC,OAAO;IAAE1B,MAAAA,OAAF;IAAW2B,MAAAA;IAAX,KAAP,KAAwC;IACpE;IACA;IACA,UAAIA,cAAc,IAAI3B,OAAO,CAACM,OAAR,CAAgBsB,GAAhB,CAAoB,OAApB,CAAtB,EAAoD;IAChD,eAAO,MAAM7B,qBAAqB,CAACC,OAAD,EAAU2B,cAAV,CAAlC;IACH,OALmE;IAOpE;;;IACA,aAAOA,cAAP;IACH,KATD;IAUH;;IAvBqB;;;;;;;;;;;"}