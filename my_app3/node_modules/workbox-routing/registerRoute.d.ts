import { <PERSON><PERSON><PERSON><PERSON>, RouteMatchCallback } from 'workbox-core/types.js';
import { Route } from './Route.js';
import { HTTPMethod } from './utils/constants.js';
import './_version.js';
/**
 * Easily register a RegExp, string, or function with a caching
 * strategy to a singleton Router instance.
 *
 * This method will generate a Route for you if needed and
 * call {@link workbox-routing.Router#registerRoute}.
 *
 * @param {RegExp|string|workbox-routing.Route~matchCallback|workbox-routing.Route} capture
 * If the capture param is a `Route`, all other arguments will be ignored.
 * @param {workbox-routing~handlerCallback} [handler] A callback
 * function that returns a Promise resulting in a Response. This parameter
 * is required if `capture` is not a `Route` object.
 * @param {string} [method='GET'] The HTTP method to match the Route
 * against.
 * @return {workbox-routing.Route} The generated `Route`.
 *
 * @memberof workbox-routing
 */
declare function registerRoute(capture: RegExp | string | RouteMatchCallback | Route, handler?: <PERSON><PERSON><PERSON><PERSON>, method?: HTTPMethod): Route;
export { registerRoute };
