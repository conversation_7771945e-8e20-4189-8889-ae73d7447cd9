{"name": "workbox-precaching", "version": "6.6.1", "license": "MIT", "author": "Google's Web DevRel Team", "description": "This module efficiently precaches assets.", "repository": "googlechrome/workbox", "bugs": "https://github.com/googlechrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "keywords": ["workbox", "workboxjs", "service worker", "sw"], "workbox": {"browserNamespace": "workbox.precaching", "packageType": "sw"}, "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "dependencies": {"workbox-core": "6.6.1", "workbox-routing": "6.6.1", "workbox-strategies": "6.6.1"}, "gitHead": "4a3c20d121efd878f3982760620f640e5bd67250"}