{"version": 3, "sources": ["../src/ExplorerSync.ts"], "names": ["ExplorerSync", "ExplorerBase", "constructor", "options", "searchSync", "searchFrom", "process", "cwd", "startDirectory", "result", "searchFromDirectorySync", "dir", "absoluteDir", "path", "resolve", "run", "searchDirectorySync", "nextDir", "nextDirectoryToSearch", "transformResult", "config", "transform", "searchCache", "place", "searchPlaces", "placeResult", "loadSearchPlaceSync", "shouldSearchStopWithResult", "filepath", "join", "content", "createCosmiconfigResultSync", "loadFileContentSync", "trim", "undefined", "loader", "getLoaderEntryForFile", "loaderResult", "fileContent", "loadedContentToCosmiconfigResult", "loadSync", "validate<PERSON>ile<PERSON><PERSON>", "absoluteFilePath", "runLoadSync", "throwNotFound", "cosmiconfigResult", "loadCache"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AACA;;AACA;;;;AAOA,MAAMA,YAAN,SAA2BC,0BAA3B,CAA6D;AACpDC,EAAAA,WAAW,CAACC,OAAD,EAA+B;AAC/C,UAAMA,OAAN;AACD;;AAEMC,EAAAA,UAAU,CAACC,UAAkB,GAAGC,OAAO,CAACC,GAAR,EAAtB,EAAwD;AACvE,UAAMC,cAAc,GAAG,oCAAiBH,UAAjB,CAAvB;AACA,UAAMI,MAAM,GAAG,KAAKC,uBAAL,CAA6BF,cAA7B,CAAf;AAEA,WAAOC,MAAP;AACD;;AAEOC,EAAAA,uBAAuB,CAACC,GAAD,EAAiC;AAC9D,UAAMC,WAAW,GAAGC,cAAKC,OAAL,CAAaR,OAAO,CAACC,GAAR,EAAb,EAA4BI,GAA5B,CAApB;;AAEA,UAAMI,GAAG,GAAG,MAAyB;AACnC,YAAMN,MAAM,GAAG,KAAKO,mBAAL,CAAyBJ,WAAzB,CAAf;AACA,YAAMK,OAAO,GAAG,KAAKC,qBAAL,CAA2BN,WAA3B,EAAwCH,MAAxC,CAAhB;;AAEA,UAAIQ,OAAJ,EAAa;AACX,eAAO,KAAKP,uBAAL,CAA6BO,OAA7B,CAAP;AACD;;AAED,YAAME,eAAe,GAAG,KAAKC,MAAL,CAAYC,SAAZ,CAAsBZ,MAAtB,CAAxB;AAEA,aAAOU,eAAP;AACD,KAXD;;AAaA,QAAI,KAAKG,WAAT,EAAsB;AACpB,aAAO,oCAAiB,KAAKA,WAAtB,EAAmCV,WAAnC,EAAgDG,GAAhD,CAAP;AACD;;AAED,WAAOA,GAAG,EAAV;AACD;;AAEOC,EAAAA,mBAAmB,CAACL,GAAD,EAAiC;AAC1D,SAAK,MAAMY,KAAX,IAAoB,KAAKH,MAAL,CAAYI,YAAhC,EAA8C;AAC5C,YAAMC,WAAW,GAAG,KAAKC,mBAAL,CAAyBf,GAAzB,EAA8BY,KAA9B,CAApB;;AAEA,UAAI,KAAKI,0BAAL,CAAgCF,WAAhC,MAAiD,IAArD,EAA2D;AACzD,eAAOA,WAAP;AACD;AACF,KAPyD,CAS1D;;;AACA,WAAO,IAAP;AACD;;AAEOC,EAAAA,mBAAmB,CAACf,GAAD,EAAcY,KAAd,EAAgD;AACzE,UAAMK,QAAQ,GAAGf,cAAKgB,IAAL,CAAUlB,GAAV,EAAeY,KAAf,CAAjB;;AACA,UAAMO,OAAO,GAAG,4BAAaF,QAAb,CAAhB;AAEA,UAAMnB,MAAM,GAAG,KAAKsB,2BAAL,CAAiCH,QAAjC,EAA2CE,OAA3C,CAAf;AAEA,WAAOrB,MAAP;AACD;;AAEOuB,EAAAA,mBAAmB,CACzBJ,QADyB,EAEzBE,OAFyB,EAGN;AACnB,QAAIA,OAAO,KAAK,IAAhB,EAAsB;AACpB,aAAO,IAAP;AACD;;AACD,QAAIA,OAAO,CAACG,IAAR,OAAmB,EAAvB,EAA2B;AACzB,aAAOC,SAAP;AACD;;AACD,UAAMC,MAAM,GAAG,KAAKC,qBAAL,CAA2BR,QAA3B,CAAf;AACA,UAAMS,YAAY,GAAGF,MAAM,CAACP,QAAD,EAAWE,OAAX,CAA3B;AAEA,WAAOO,YAAP;AACD;;AAEON,EAAAA,2BAA2B,CACjCH,QADiC,EAEjCE,OAFiC,EAGd;AACnB,UAAMQ,WAAW,GAAG,KAAKN,mBAAL,CAAyBJ,QAAzB,EAAmCE,OAAnC,CAApB;AACA,UAAMrB,MAAM,GAAG,KAAK8B,gCAAL,CAAsCX,QAAtC,EAAgDU,WAAhD,CAAf;AAEA,WAAO7B,MAAP;AACD;;AAEM+B,EAAAA,QAAQ,CAACZ,QAAD,EAAsC;AACnD,SAAKa,gBAAL,CAAsBb,QAAtB;;AACA,UAAMc,gBAAgB,GAAG7B,cAAKC,OAAL,CAAaR,OAAO,CAACC,GAAR,EAAb,EAA4BqB,QAA5B,CAAzB;;AAEA,UAAMe,WAAW,GAAG,MAAyB;AAC3C,YAAMb,OAAO,GAAG,4BAAaY,gBAAb,EAA+B;AAAEE,QAAAA,aAAa,EAAE;AAAjB,OAA/B,CAAhB;AACA,YAAMC,iBAAiB,GAAG,KAAKd,2BAAL,CACxBW,gBADwB,EAExBZ,OAFwB,CAA1B;AAKA,YAAMX,eAAe,GAAG,KAAKC,MAAL,CAAYC,SAAZ,CAAsBwB,iBAAtB,CAAxB;AAEA,aAAO1B,eAAP;AACD,KAVD;;AAYA,QAAI,KAAK2B,SAAT,EAAoB;AAClB,aAAO,oCAAiB,KAAKA,SAAtB,EAAiCJ,gBAAjC,EAAmDC,WAAnD,CAAP;AACD;;AAED,WAAOA,WAAW,EAAlB;AACD;;AAxG0D", "sourcesContent": ["import path from 'path';\nimport { ExplorerBase } from './ExplorerBase';\nimport { readFileSync } from './readFile';\nimport { cacheWrapperSync } from './cacheWrapper';\nimport { getDirectorySync } from './getDirectory';\nimport {\n  CosmiconfigResult,\n  ExplorerOptionsSync,\n  LoadedFileContent,\n} from './types';\n\nclass ExplorerSync extends ExplorerBase<ExplorerOptionsSync> {\n  public constructor(options: ExplorerOptionsSync) {\n    super(options);\n  }\n\n  public searchSync(searchFrom: string = process.cwd()): CosmiconfigResult {\n    const startDirectory = getDirectorySync(searchFrom);\n    const result = this.searchFromDirectorySync(startDirectory);\n\n    return result;\n  }\n\n  private searchFromDirectorySync(dir: string): CosmiconfigResult {\n    const absoluteDir = path.resolve(process.cwd(), dir);\n\n    const run = (): CosmiconfigResult => {\n      const result = this.searchDirectorySync(absoluteDir);\n      const nextDir = this.nextDirectoryToSearch(absoluteDir, result);\n\n      if (nextDir) {\n        return this.searchFromDirectorySync(nextDir);\n      }\n\n      const transformResult = this.config.transform(result);\n\n      return transformResult;\n    };\n\n    if (this.searchCache) {\n      return cacheWrapperSync(this.searchCache, absoluteDir, run);\n    }\n\n    return run();\n  }\n\n  private searchDirectorySync(dir: string): CosmiconfigResult {\n    for (const place of this.config.searchPlaces) {\n      const placeResult = this.loadSearchPlaceSync(dir, place);\n\n      if (this.shouldSearchStopWithResult(placeResult) === true) {\n        return placeResult;\n      }\n    }\n\n    // config not found\n    return null;\n  }\n\n  private loadSearchPlaceSync(dir: string, place: string): CosmiconfigResult {\n    const filepath = path.join(dir, place);\n    const content = readFileSync(filepath);\n\n    const result = this.createCosmiconfigResultSync(filepath, content);\n\n    return result;\n  }\n\n  private loadFileContentSync(\n    filepath: string,\n    content: string | null,\n  ): LoadedFileContent {\n    if (content === null) {\n      return null;\n    }\n    if (content.trim() === '') {\n      return undefined;\n    }\n    const loader = this.getLoaderEntryForFile(filepath);\n    const loaderResult = loader(filepath, content);\n\n    return loaderResult;\n  }\n\n  private createCosmiconfigResultSync(\n    filepath: string,\n    content: string | null,\n  ): CosmiconfigResult {\n    const fileContent = this.loadFileContentSync(filepath, content);\n    const result = this.loadedContentToCosmiconfigResult(filepath, fileContent);\n\n    return result;\n  }\n\n  public loadSync(filepath: string): CosmiconfigResult {\n    this.validateFilePath(filepath);\n    const absoluteFilePath = path.resolve(process.cwd(), filepath);\n\n    const runLoadSync = (): CosmiconfigResult => {\n      const content = readFileSync(absoluteFilePath, { throwNotFound: true });\n      const cosmiconfigResult = this.createCosmiconfigResultSync(\n        absoluteFilePath,\n        content,\n      );\n\n      const transformResult = this.config.transform(cosmiconfigResult);\n\n      return transformResult;\n    };\n\n    if (this.loadCache) {\n      return cacheWrapperSync(this.loadCache, absoluteFilePath, runLoadSync);\n    }\n\n    return runLoadSync();\n  }\n}\n\nexport { ExplorerSync };\n"], "file": "ExplorerSync.js"}