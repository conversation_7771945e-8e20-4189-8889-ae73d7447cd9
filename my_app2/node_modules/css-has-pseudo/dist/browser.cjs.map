{"version": 3, "file": "browser.cjs", "sources": ["../src/browser.js"], "sourcesContent": ["/* global MutationObserver,requestAnimationFrame */\nexport default function cssHasPseudo(document) {\n\tconst observedItems = [];\n\n\t// document.createAttribute() doesn't support `:` in the name. innerHTML does\n\tconst attributeElement = document.createElement('x');\n\n\t// walk all stylesheets to collect observed css rules\n\t[].forEach.call(document.styleSheets, walkStyleSheet);\n\ttransformObservedItems();\n\n\t// observe DOM modifications that affect selectors\n\tconst mutationObserver = new MutationObserver(mutationsList => {\n\t\tmutationsList.forEach(mutation => {\n\t\t\t[].forEach.call(mutation.addedNodes || [], node => {\n\t\t\t\t// walk stylesheets to collect observed css rules\n\t\t\t\tif (node.nodeType === 1 && node.sheet) {\n\t\t\t\t\twalkStyleSheet(node.sheet);\n\t\t\t\t}\n\t\t\t});\n\n\t\t\t// transform observed css rules\n\t\t\tcleanupObservedCssRules();\n\t\t\ttransformObservedItems();\n\t\t});\n\t});\n\n\tmutationObserver.observe(document, { childList: true, subtree: true });\n\n\t// observe DOM events that affect pseudo-selectors\n\tdocument.addEventListener('focus', transformObservedItems, true);\n\tdocument.addEventListener('blur', transformObservedItems, true);\n\tdocument.addEventListener('input', transformObservedItems);\n\n\t// transform observed css rules\n\tfunction transformObservedItems () {\n\t\trequestAnimationFrame(() => {\n\t\t\tobservedItems.forEach(\n\t\t\t\titem => {\n\t\t\t\t\tconst nodes = [];\n\n\t\t\t\t\t[].forEach.call(\n\t\t\t\t\t\tdocument.querySelectorAll(item.scopeSelector),\n\t\t\t\t\t\telement => {\n\t\t\t\t\t\t\tconst nthChild = [].indexOf.call(element.parentNode.children, element) + 1;\n\t\t\t\t\t\t\tconst relativeSelectors = item.relativeSelectors.map(\n\t\t\t\t\t\t\t\trelativeSelector => item.scopeSelector + ':nth-child(' + nthChild + ') ' + relativeSelector,\n\t\t\t\t\t\t\t).join();\n\n\t\t\t\t\t\t\t// find any relative :has element from the :scope element\n\t\t\t\t\t\t\tconst relativeElement = element.parentNode.querySelector(relativeSelectors);\n\n\t\t\t\t\t\t\tconst shouldElementMatch = item.isNot ? !relativeElement : relativeElement;\n\n\t\t\t\t\t\t\tif (shouldElementMatch) {\n\t\t\t\t\t\t\t\t// memorize the node\n\t\t\t\t\t\t\t\tnodes.push(element);\n\n\t\t\t\t\t\t\t\t// set an attribute with an irregular attribute name\n\t\t\t\t\t\t\t\t// document.createAttribute() doesn't support special characters\n\t\t\t\t\t\t\t\tattributeElement.innerHTML = '<x ' + item.attributeName + '>';\n\n\t\t\t\t\t\t\t\telement.setAttributeNode(attributeElement.children[0].attributes[0].cloneNode());\n\n\t\t\t\t\t\t\t\t// trigger a style refresh in IE and Edge\n\t\t\t\t\t\t\t\tdocument.documentElement.style.zoom = 1; document.documentElement.style.zoom = null;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t);\n\n\t\t\t\t\t// remove the encoded attribute from all nodes that no longer match them\n\t\t\t\t\titem.nodes.forEach(node => {\n\t\t\t\t\t\tif (nodes.indexOf(node) === -1) {\n\t\t\t\t\t\t\tnode.removeAttribute(item.attributeName);\n\n\t\t\t\t\t\t\t// trigger a style refresh in IE and Edge\n\t\t\t\t\t\t\tdocument.documentElement.style.zoom = 1; document.documentElement.style.zoom = null;\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\t// update the\n\t\t\t\t\titem.nodes = nodes;\n\t\t\t\t},\n\t\t\t);\n\t\t});\n\t}\n\n\t// remove any observed cssrules that no longer apply\n\tfunction cleanupObservedCssRules () {\n\t\t[].push.apply(\n\t\t\tobservedItems,\n\t\t\tobservedItems.splice(0).filter(\n\t\t\t\titem => item.rule.parentStyleSheet &&\n\t\t\t\t\titem.rule.parentStyleSheet.ownerNode &&\n\t\t\t\t\tdocument.documentElement.contains(item.rule.parentStyleSheet.ownerNode),\n\t\t\t),\n\t\t);\n\t}\n\n\t// walk a stylesheet to collect observed css rules\n\tfunction walkStyleSheet (styleSheet) {\n\t\ttry {\n\t\t\t// walk a css rule to collect observed css rules\n\t\t\t[].forEach.call(styleSheet.cssRules || [], rule => {\n\t\t\t\tif (rule.selectorText) {\n\t\t\t\t\t// decode the selector text in all browsers to:\n\t\t\t\t\t// [1] = :scope, [2] = :not(:has), [3] = :has relative, [4] = :scope relative\n\t\t\t\t\tconst selectors = decodeURIComponent(rule.selectorText.replace(/\\\\(.)/g, '$1')).match(/^(.*?)\\[:(not-)?has\\((.+?)\\)\\](.*?)$/);\n\n\t\t\t\t\tif (selectors) {\n\t\t\t\t\t\tconst attributeName = ':' + (selectors[2] ? 'not-' : '') + 'has(' +\n\t\t\t\t\t\t\t// encode a :has() pseudo selector as an attribute name\n\t\t\t\t\t\t\tencodeURIComponent(selectors[3]).replace(/%3A/g, ':').replace(/%5B/g, '[').replace(/%5D/g, ']').replace(/%2C/g, ',') +\n\t\t\t\t\t\t')';\n\n\t\t\t\t\t\tobservedItems.push({\n\t\t\t\t\t\t\trule,\n\t\t\t\t\t\t\tscopeSelector: selectors[1],\n\t\t\t\t\t\t\tisNot: selectors[2],\n\t\t\t\t\t\t\trelativeSelectors: selectors[3].split(/\\s*,\\s*/),\n\t\t\t\t\t\t\tattributeName,\n\t\t\t\t\t\t\tnodes: [],\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\twalkStyleSheet(rule);\n\t\t\t\t}\n\t\t\t});\n\t\t} catch (error) {\n\t\t\t/* do nothing and continue */\n\t\t}\n\t}\n}\n"], "names": ["document", "observedItems", "attributeElement", "createElement", "transformObservedItems", "requestAnimationFrame", "for<PERSON>ach", "item", "nodes", "call", "querySelectorAll", "scopeSelector", "element", "nthChild", "indexOf", "parentNode", "children", "relativeSelectors", "map", "relativeSelector", "join", "relativeElement", "querySelector", "isNot", "push", "innerHTML", "attributeName", "setAttributeNode", "attributes", "cloneNode", "documentElement", "style", "zoom", "node", "removeAttribute", "walkStyleSheet", "styleSheet", "cssRules", "rule", "selectorText", "selectors", "decodeURIComponent", "replace", "match", "encodeURIComponent", "split", "error", "styleSheets", "MutationObserver", "mutationsList", "mutation", "addedNodes", "nodeType", "sheet", "apply", "splice", "filter", "parentStyleSheet", "ownerNode", "contains", "observe", "childList", "subtree", "addEventListener"], "mappings": "eACe,SAAsBA,OAC9BC,EAAgB,GAGhBC,EAAmBF,EAASG,cAAc,cA8BvCC,IACRC,uBAAsB,WACrBJ,EAAcK,SACb,SAAAC,OACOC,EAAQ,MAEXF,QAAQG,KACVT,EAASU,iBAAiBH,EAAKI,gBAC/B,SAAAC,OACOC,EAAW,GAAGC,QAAQL,KAAKG,EAAQG,WAAWC,SAAUJ,GAAW,EACnEK,EAAoBV,EAAKU,kBAAkBC,KAChD,SAAAC,UAAoBZ,EAAKI,cAAgB,cAAgBE,EAAW,KAAOM,KAC1EC,OAGIC,EAAkBT,EAAQG,WAAWO,cAAcL,IAE9BV,EAAKgB,OAASF,EAAkBA,KAI1Db,EAAMgB,KAAKZ,GAIXV,EAAiBuB,UAAY,MAAQlB,EAAKmB,cAAgB,IAE1Dd,EAAQe,iBAAiBzB,EAAiBc,SAAS,GAAGY,WAAW,GAAGC,aAGpE7B,EAAS8B,gBAAgBC,MAAMC,KAAO,EAAGhC,EAAS8B,gBAAgBC,MAAMC,KAAO,SAMlFzB,EAAKC,MAAMF,SAAQ,SAAA2B,IACW,IAAzBzB,EAAMM,QAAQmB,KACjBA,EAAKC,gBAAgB3B,EAAKmB,eAG1B1B,EAAS8B,gBAAgBC,MAAMC,KAAO,EAAGhC,EAAS8B,gBAAgBC,MAAMC,KAAO,SAKjFzB,EAAKC,MAAQA,iBAmBR2B,EAAgBC,UAGpB9B,QAAQG,KAAK2B,EAAWC,UAAY,IAAI,SAAAC,MACtCA,EAAKC,aAAc,KAGhBC,EAAYC,mBAAmBH,EAAKC,aAAaG,QAAQ,SAAU,OAAOC,MAAM,2CAElFH,EAAW,KACRd,EAAgB,KAAOc,EAAU,GAAK,OAAS,IAAM,OAE1DI,mBAAmBJ,EAAU,IAAIE,QAAQ,OAAQ,KAAKA,QAAQ,OAAQ,KAAKA,QAAQ,OAAQ,KAAKA,QAAQ,OAAQ,KACjH,IAEAzC,EAAcuB,KAAK,CAClBc,KAAAA,EACA3B,cAAe6B,EAAU,GACzBjB,MAAOiB,EAAU,GACjBvB,kBAAmBuB,EAAU,GAAGK,MAAM,WACtCnB,cAAAA,EACAlB,MAAO,WAIT2B,EAAeG,MAGhB,MAAOQ,QAxHPxC,QAAQG,KAAKT,EAAS+C,YAAaZ,GACtC/B,IAGyB,IAAI4C,kBAAiB,SAAAC,GAC7CA,EAAc3C,SAAQ,SAAA4C,MAClB5C,QAAQG,KAAKyC,EAASC,YAAc,IAAI,SAAAlB,GAEpB,IAAlBA,EAAKmB,UAAkBnB,EAAKoB,OAC/BlB,EAAeF,EAAKoB,aAwEpB7B,KAAK8B,MACPrD,EACAA,EAAcsD,OAAO,GAAGC,QACvB,SAAAjD,UAAQA,EAAK+B,KAAKmB,kBACjBlD,EAAK+B,KAAKmB,iBAAiBC,WAC3B1D,EAAS8B,gBAAgB6B,SAASpD,EAAK+B,KAAKmB,iBAAiBC,eAvE/DtD,UAIewD,QAAQ5D,EAAU,CAAE6D,WAAW,EAAMC,SAAS,IAG/D9D,EAAS+D,iBAAiB,QAAS3D,GAAwB,GAC3DJ,EAAS+D,iBAAiB,OAAQ3D,GAAwB,GAC1DJ,EAAS+D,iBAAiB,QAAS3D"}