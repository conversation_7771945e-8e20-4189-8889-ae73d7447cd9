{"version": 3, "file": "browser.mjs", "sources": ["../src/browser.js"], "sourcesContent": ["/* global document,window,matchMedia */\nconst colorIndexRegExp = /((?:not )?all and )?(\\(color-index: *(22|48|70)\\))/i;\nconst prefersColorSchemeRegExp = /prefers-color-scheme:/i;\n\nconst prefersColorSchemeInit = initialColorScheme => {\n\tconst mediaQueryString = '(prefers-color-scheme: dark)';\n\tconst mediaQueryList = window.matchMedia && matchMedia(mediaQueryString);\n\tconst hasNativeSupport = mediaQueryList && mediaQueryList.media === mediaQueryString;\n\tconst mediaQueryListener = () => {\n\t\tset(mediaQueryList.matches ? 'dark' : 'light');\n\t};\n\tconst removeListener = () => {\n\t\tif (mediaQueryList) {\n\t\t\tmediaQueryList.removeListener(mediaQueryListener);\n\t\t}\n\t};\n\tconst set = colorScheme => {\n\t\tif (colorScheme !== currentColorScheme) {\n\t\t\tcurrentColorScheme = colorScheme;\n\n\t\t\tif (typeof result.onChange === 'function') {\n\t\t\t\tresult.onChange();\n\t\t\t}\n\t\t}\n\n\t\t[].forEach.call(document.styleSheets || [], styleSheet => {\n\n\t\t\t// cssRules is a live list. Converting to an Array first.\n\t\t\tconst rules = [];\n\t\t\t[].forEach.call(styleSheet.cssRules || [], cssRule => {\n\t\t\t\trules.push(cssRule);\n\t\t\t});\n\n\t\t\trules.forEach(cssRule => {\n\t\t\t\tconst colorSchemeMatch = prefersColorSchemeRegExp.test(Object(cssRule.media).mediaText);\n\n\t\t\t\tif (colorSchemeMatch) {\n\t\t\t\t\tconst index = [].indexOf.call(cssRule.parentStyleSheet.cssRules, cssRule);\n\t\t\t\t\tcssRule.parentStyleSheet.deleteRule(index);\n\t\t\t\t} else {\n\t\t\t\t\tconst colorIndexMatch = (Object(cssRule.media).mediaText || '').match(colorIndexRegExp);\n\n\t\t\t\t\tif (colorIndexMatch) {\n\t\t\t\t\t\t// Old style which has poor browser support and can't handle complex media queries.\n\t\t\t\t\t\tcssRule.media.mediaText = (\n\t\t\t\t\t\t\t(/^dark$/i.test(colorScheme)\n\t\t\t\t\t\t\t\t? colorIndexMatch[3] === '48'\n\t\t\t\t\t\t\t\t: /^light$/i.test(colorScheme)\n\t\t\t\t\t\t\t\t\t? colorIndexMatch[3] === '70'\n\t\t\t\t\t\t\t\t\t: colorIndexMatch[3] === '22')\n\t\t\t\t\t\t\t\t? 'not all and '\n\t\t\t\t\t\t\t\t: ''\n\t\t\t\t\t\t) + cssRule.media.mediaText.replace(colorIndexRegExp, '$2');\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// New style which supports complex media queries.\n\t\t\t\t\t\tconst colorDepthMatch = (Object(cssRule.media).mediaText || '').match(/\\( *(?:color|max-color): *(48842621|70318723|22511989) *\\)/i);\n\t\t\t\t\t\tif (colorDepthMatch && colorDepthMatch.length > 1) {\n\t\t\t\t\t\t\tif (/^dark$/i.test(colorScheme) && (colorDepthMatch[1] === '48842621' || colorDepthMatch[1] === '22511989')) {\n\t\t\t\t\t\t\t\t// No preference or preferred is dark and rule is dark.\n\t\t\t\t\t\t\t\tcssRule.media.mediaText = cssRule.media.mediaText.replace(/\\( *color: *(?:48842621|70318723) *\\)/i, `(max-color: ${colorDepthMatch[1]})`);\n\t\t\t\t\t\t\t} else if (/^light$/i.test(colorScheme) && (colorDepthMatch[1] === '70318723' || colorDepthMatch[1] === '22511989')) {\n\t\t\t\t\t\t\t\t// No preference or preferred is light and rule is light.\n\t\t\t\t\t\t\t\tcssRule.media.mediaText = cssRule.media.mediaText.replace(/\\( *color: *(?:48842621|22511989) *\\)/i, `(max-color: ${colorDepthMatch[1]})`);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tcssRule.media.mediaText = cssRule.media.mediaText.replace(/\\( *max-color: *(?:48842621|70318723|22511989) *\\)/i, `(color: ${colorDepthMatch[1]})`);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t};\n\tconst result = Object.defineProperty(\n\t\t{ hasNativeSupport, removeListener },\n\t\t'scheme',\n\t\t{ get: () => currentColorScheme, set },\n\t);\n\n\t// initialize the color scheme using the provided value, the system value, or light\n\tlet currentColorScheme = initialColorScheme || (mediaQueryList && mediaQueryList.matches ? 'dark' : 'light');\n\n\tset(currentColorScheme);\n\n\t// listen for system changes\n\tif (mediaQueryList) {\n\t\tif ('addEventListener' in mediaQueryList) {\n\t\t\tmediaQueryList.addEventListener('change', mediaQueryListener);\n\t\t} else {\n\t\t\tmediaQueryList.addListener(mediaQueryListener);\n\t\t}\n\t}\n\n\treturn result;\n};\n\nexport default prefersColorSchemeInit;\n"], "names": ["colorIndexRegExp", "prefersColorSchemeRegExp", "prefersColorSchemeInit", "initialColorScheme", "mediaQueryString", "mediaQueryList", "window", "matchMedia", "hasNativeSupport", "media", "mediaQueryListener", "set", "matches", "colorScheme", "currentColorScheme", "result", "onChange", "for<PERSON>ach", "call", "document", "styleSheets", "styleSheet", "rules", "cssRules", "cssRule", "push", "test", "Object", "mediaText", "index", "indexOf", "parentStyleSheet", "deleteRule", "colorIndexMatch", "match", "replace", "colorDepthMatch", "length", "defineProperty", "removeListener", "get", "addEventListener", "addListener"], "mappings": "AACA,IAAMA,EAAmB,sDACnBC,EAA2B,yBAE3BC,EAAyB,SAAAC,OACxBC,EAAmB,+BACnBC,EAAiBC,OAAOC,YAAcA,WAAWH,GACjDI,EAAmBH,GAAkBA,EAAeI,QAAUL,EAC9DM,EAAqB,WAC1BC,EAAIN,EAAeO,QAAU,OAAS,UAOjCD,EAAM,SAAAE,GACPA,IAAgBC,IACnBA,EAAqBD,EAEU,mBAApBE,EAAOC,UACjBD,EAAOC,eAINC,QAAQC,KAAKC,SAASC,aAAe,IAAI,SAAAC,OAGrCC,EAAQ,MACXL,QAAQC,KAAKG,EAAWE,UAAY,IAAI,SAAAC,GAC1CF,EAAMG,KAAKD,MAGZF,EAAML,SAAQ,SAAAO,MACYvB,EAAyByB,KAAKC,OAAOH,EAAQf,OAAOmB,WAEvD,KACfC,EAAQ,GAAGC,QAAQZ,KAAKM,EAAQO,iBAAiBR,SAAUC,GACjEA,EAAQO,iBAAiBC,WAAWH,OAC9B,KACAI,GAAmBN,OAAOH,EAAQf,OAAOmB,WAAa,IAAIM,MAAMlC,MAElEiC,EAEHT,EAAQf,MAAMmB,YACZ,UAAUF,KAAKb,GACU,OAAvBoB,EAAgB,GAChB,WAAWP,KAAKb,GACQ,OAAvBoB,EAAgB,GACO,OAAvBA,EAAgB,IACjB,eACA,IACAT,EAAQf,MAAMmB,UAAUO,QAAQnC,EAAkB,UAChD,KAEAoC,GAAmBT,OAAOH,EAAQf,OAAOmB,WAAa,IAAIM,MAAM,+DAClEE,GAAmBA,EAAgBC,OAAS,KAC3C,UAAUX,KAAKb,IAAwC,aAAvBuB,EAAgB,IAA4C,aAAvBA,EAAgB,IAG9E,WAAWV,KAAKb,IAAwC,aAAvBuB,EAAgB,IAA4C,aAAvBA,EAAgB,GAIhGZ,EAAQf,MAAMmB,UAAYJ,EAAQf,MAAMmB,UAAUO,QAAQ,iEAAkEC,EAAgB,QAF5IZ,EAAQf,MAAMmB,UAAYJ,EAAQf,MAAMmB,UAAUO,QAAQ,wDAAyDC,EAAgB,QAHnIZ,EAAQf,MAAMmB,UAAYJ,EAAQf,MAAMmB,UAAUO,QAAQ,wDAAyDC,EAAgB,kBAapIrB,EAASY,OAAOW,eACrB,CAAE9B,iBAAAA,EAAkB+B,eA9DE,WAClBlC,GACHA,EAAekC,eAAe7B,KA6D/B,SACA,CAAE8B,IAAK,kBAAM1B,GAAoBH,IAAAA,IAI9BG,EAAqBX,IAAuBE,GAAkBA,EAAeO,QAAU,OAAS,gBAEpGD,EAAIG,GAGAT,IACC,qBAAsBA,EACzBA,EAAeoC,iBAAiB,SAAU/B,GAE1CL,EAAeqC,YAAYhC,IAItBK"}