{"name": "dojo-blog", "version": "1.0.0", "private": true, "description": "", "react-project": {"server": "modules/server.js", "client": "modules/client.js", "webpack": "webpack.config.js"}, "scripts": {"start": "if-env NODE_ENV=production && npm run react-project:start:prod || npm run react-project:start:dev", "test": "karma start", "react-project:start:dev": "eslint modules && react-project start", "react-project:start:prod": "rm -rf .build && react-project build && node .build/server.js"}, "author": "", "license": "ISC", "dependencies": {"@babel/core": "7.28.0", "babel-cli": "6.26.0", "babel-core": "6.5.1", "babel-eslint": "10.1.0", "babel-loader": "10.0.0", "babel-preset-es2015": "6.5.0", "babel-preset-react": "6.5.0", "babel-preset-react-hmre": "1.1.0", "babel-preset-stage-1": "6.24.1", "body-parser": "1.20.3", "bundle-loader": "0.5.6", "compression": "1.8.0", "css-loader": "7.1.2", "dotenv": "2.0.0", "eslint": "9.30.1", "eslint-config-rackt": "1.1.1", "eslint-plugin-react": "3.16.1", "expect": "1.14.0", "express": "4.13.4", "extract-text-webpack-plugin": "3.0.2", "file-loader": "6.2.0", "helmet": "8.1.0", "hpp": "0.2.3", "if-env": "1.0.0", "karma": "6.4.4", "karma-chrome-launcher": "0.2.2", "karma-mocha": "0.2.2", "karma-mocha-reporter": "1.2.0", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "5.0.1", "mocha": "11.7.1", "morgan": "1.10.0", "node-fetch": "3.3.2", "null-loader": "0.1.1", "postcss-loader": "8.1.1", "react": "19.1.0", "react-dom": "19.1.0", "react-project": "0.0.30", "react-router": "2.0.0", "react-scripts": "5.0.1", "react-title-component": "1.0.1", "source-map-support": "0.4.0", "style-loader": "0.13.2", "url-loader": "0.5.9", "webpack": "5.99.9", "webpack-dev-server": "5.2.2"}}