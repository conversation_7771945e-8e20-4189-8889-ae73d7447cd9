{"name": "range-parser", "author": "<PERSON><PERSON> <<EMAIL>> (http://tjholowaychuk.com)", "description": "Range header field string parser", "version": "1.0.3", "license": "MIT", "keywords": ["range", "parser", "http"], "repository": "jshttp/range-parser", "devDependencies": {"istanbul": "0.4.0", "mocha": "1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}}