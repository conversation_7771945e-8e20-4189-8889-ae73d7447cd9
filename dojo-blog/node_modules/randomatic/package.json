{"name": "randomatic", "description": "Generate randomized strings of a specified length using simple character sequences. The original generate-password.", "version": "3.1.1", "homepage": "https://github.com/jonschlinkert/randomatic", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON><PERSON><PERSON> (https://github.com/TrySound)", "<PERSON><PERSON><PERSON> (http://dragosfotescu.com)", "<PERSON><PERSON>z ul haque (http://www.10pearls.com)", "<PERSON> (http://twitter.com/jonschlink<PERSON>)", "<PERSON> (http://michaelrhod.es)", "<PERSON> (https://paulmillr.com)", "<PERSON><PERSON><PERSON> (www.rouvenwessling.de)", "<PERSON> (https://sunknudsen.com)"], "repository": "jonschlinkert/randomatic", "bugs": {"url": "https://github.com/jonschlinkert/randomatic/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^4.0.0", "kind-of": "^6.0.0", "math-random": "^1.0.1"}, "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^1.1.1", "glob": "^7.1.2", "gulp-format-md": "^0.1.12", "mocha": "^3.4.2"}, "keywords": ["alpha", "alpha-numeric", "alphanumeric", "characters", "chars", "generate", "generate-password", "numeric", "password", "rand", "random", "randomatic", "randomize", "randomized"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["pad-left", "pad-right", "repeat-string"]}, "lint": {"reflinks": true}}}