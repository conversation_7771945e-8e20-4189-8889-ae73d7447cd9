/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';(function(b,d){"object"===typeof exports&&"undefined"!==typeof module?d(exports):"function"===typeof define&&define.amd?define(["exports"],d):(b=b||self,d(b.ReactIs={}))})(this,function(b){function d(a){if("object"===typeof a&&null!==a){var b=a.$$typeof;switch(b){case r:switch(a=a.type,a){case t:case e:case f:case g:case h:case k:return a;default:switch(a=a&&a.$$typeof,a){case l:case m:case n:case p:case q:return a;default:return b}}case u:return b}}}function v(a){return d(a)===e}var c=
"function"===typeof Symbol&&Symbol.for,r=c?Symbol.for("react.element"):60103,u=c?Symbol.for("react.portal"):60106,f=c?Symbol.for("react.fragment"):60107,h=c?Symbol.for("react.strict_mode"):60108,g=c?Symbol.for("react.profiler"):60114,q=c?Symbol.for("react.provider"):60109,l=c?Symbol.for("react.context"):60110,t=c?Symbol.for("react.async_mode"):60111,e=c?Symbol.for("react.concurrent_mode"):60111,m=c?Symbol.for("react.forward_ref"):60112,k=c?Symbol.for("react.suspense"):60113,w=c?Symbol.for("react.suspense_list"):
60120,p=c?Symbol.for("react.memo"):60115,n=c?Symbol.for("react.lazy"):60116,x=c?Symbol.for("react.block"):60121,y=c?Symbol.for("react.fundamental"):60117,z=c?Symbol.for("react.responder"):60118,A=c?Symbol.for("react.scope"):60119;b.AsyncMode=t;b.ConcurrentMode=e;b.ContextConsumer=l;b.ContextProvider=q;b.Element=r;b.ForwardRef=m;b.Fragment=f;b.Lazy=n;b.Memo=p;b.Portal=u;b.Profiler=g;b.StrictMode=h;b.Suspense=k;b.isAsyncMode=function(a){return v(a)||d(a)===t};b.isConcurrentMode=v;b.isContextConsumer=
function(a){return d(a)===l};b.isContextProvider=function(a){return d(a)===q};b.isElement=function(a){return"object"===typeof a&&null!==a&&a.$$typeof===r};b.isForwardRef=function(a){return d(a)===m};b.isFragment=function(a){return d(a)===f};b.isLazy=function(a){return d(a)===n};b.isMemo=function(a){return d(a)===p};b.isPortal=function(a){return d(a)===u};b.isProfiler=function(a){return d(a)===g};b.isStrictMode=function(a){return d(a)===h};b.isSuspense=function(a){return d(a)===k};b.isValidElementType=
function(a){return"string"===typeof a||"function"===typeof a||a===f||a===e||a===g||a===h||a===k||a===w||"object"===typeof a&&null!==a&&(a.$$typeof===n||a.$$typeof===p||a.$$typeof===q||a.$$typeof===l||a.$$typeof===m||a.$$typeof===y||a.$$typeof===z||a.$$typeof===A||a.$$typeof===x)};b.typeOf=d});
