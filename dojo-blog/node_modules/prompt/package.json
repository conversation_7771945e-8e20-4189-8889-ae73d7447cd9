{"name": "prompt", "version": "1.0.0", "description": "A beautiful command-line prompt for node.js", "author": "Nodejitsu Inc. <<EMAIL>>", "maintainers": ["indexzero <<EMAIL>>", "j<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "http://github.com/flatiron/prompt.git"}, "dependencies": {"colors": "^1.1.2", "pkginfo": "0.x.x", "read": "1.0.x", "revalidator": "0.1.x", "utile": "0.3.x", "winston": "2.1.x"}, "devDependencies": {"vows": "0.7.0"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "license": "MIT", "engines": {"node": ">= 0.6.6"}}